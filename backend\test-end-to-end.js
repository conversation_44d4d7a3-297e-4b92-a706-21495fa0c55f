const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testEndToEndUserJourney() {
  console.log('🚀 Starting End-to-End User Journey Testing...\n');
  console.log('=' .repeat(80));
  
  try {
    // === SCENARIO 1: NEW USER REGISTRATION AND PROPERTY SEARCH ===
    console.log('\n📋 SCENARIO 1: New User Registration and Property Search');
    console.log('-' .repeat(60));
    
    // Step 1: User registers
    console.log('\n1. User Registration...');
    const newUser = {
      name: '<PERSON>',
      email: `ahmad.khan.${Date.now()}@example.com`,
      password: 'SecurePassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, newUser);
    const userToken = registerResponse.data.data.tokens.accessToken;
    const userId = registerResponse.data.data.user.id;
    console.log('✅ User registered successfully:', newUser.name);
    
    await delay(1000);
    
    // Step 2: User searches for properties
    console.log('\n2. Browsing available properties...');
    const propertiesResponse = await axios.get(`${BASE_URL}/properties`);
    console.log(`✅ Found ${propertiesResponse.data.data?.length || 0} properties available`);
    
    await delay(1000);
    
    // Step 3: User searches for specific properties
    console.log('\n3. Searching for houses...');
    const searchResponse = await axios.get(`${BASE_URL}/properties/search?q=house`);
    console.log(`✅ Found ${searchResponse.data.data?.length || 0} houses in search results`);
    
    await delay(1000);
    
    // === SCENARIO 2: PROPERTY OWNER CREATES LISTING ===
    console.log('\n\n📋 SCENARIO 2: Property Owner Creates Listing');
    console.log('-' .repeat(60));
    
    // Step 1: Create property owner
    console.log('\n1. Property owner registration...');
    const propertyOwner = {
      name: 'Fatima Ahmadi',
      email: `fatima.ahmadi.${Date.now()}@example.com`,
      password: 'OwnerPassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const ownerResponse = await axios.post(`${BASE_URL}/auth/register`, propertyOwner);
    const ownerToken = ownerResponse.data.data.tokens.accessToken;
    const ownerId = ownerResponse.data.data.user.id;
    console.log('✅ Property owner registered:', propertyOwner.name);
    
    await delay(1000);
    
    // Step 2: Owner creates property listing
    console.log('\n2. Creating property listing...');
    const propertyData = {
      title: 'Beautiful Family House in Kabul',
      description: 'A spacious and modern family house located in a peaceful neighborhood of Kabul. Perfect for families looking for comfort and security.',
      price: 280000,
      category: 'SALE',
      type: 'HOUSE',
      province: 'Kabul',
      city: 'Kabul',
      district: 'District 5',
      address: '123 Peace Street, Kabul',
      area: 2200,
      bedrooms: 4,
      bathrooms: 3,
      features: ['parking', 'garden', 'security', 'balcony'],
      amenities: ['wifi', 'heating', 'ac', 'generator']
    };
    
    const createPropertyResponse = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { Authorization: `Bearer ${ownerToken}` }
    });
    const propertyId = createPropertyResponse.data.data._id || createPropertyResponse.data.data.id;
    console.log('✅ Property listing created:', propertyData.title);
    
    await delay(1000);
    
    // === SCENARIO 3: USER DISCOVERS AND FAVORITES PROPERTY ===
    console.log('\n\n📋 SCENARIO 3: User Discovers and Favorites Property');
    console.log('-' .repeat(60));
    
    // Step 1: User finds the new property
    console.log('\n1. User discovers new property...');
    const propertyDetailResponse = await axios.get(`${BASE_URL}/properties/${propertyId}`);
    console.log('✅ User views property details:', propertyDetailResponse.data.data.title);
    console.log('   Price:', propertyDetailResponse.data.data.price);
    console.log('   Location:', propertyDetailResponse.data.data.city, propertyDetailResponse.data.data.province);
    
    await delay(1000);
    
    // Step 2: User adds property to favorites
    console.log('\n2. User adds property to favorites...');
    const addFavoriteResponse = await axios.post(`${BASE_URL}/users/favorites/${propertyId}`, {}, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Property added to favorites');
    
    await delay(1000);
    
    // === SCENARIO 4: USER INQUIRES ABOUT PROPERTY ===
    console.log('\n\n📋 SCENARIO 4: User Inquires About Property');
    console.log('-' .repeat(60));
    
    // Step 1: User sends inquiry message
    console.log('\n1. User sends inquiry message...');
    const messageData = {
      receiverId: ownerId,
      propertyId: propertyId,
      subject: 'Inquiry about your beautiful house',
      content: 'Hello! I am very interested in your property listing. Could we schedule a viewing? I am looking for a family home and this seems perfect. Please let me know your availability. Thank you!'
    };
    
    const sendMessageResponse = await axios.post(`${BASE_URL}/messages`, messageData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Inquiry message sent to property owner');
    
    await delay(1000);
    
    // Step 2: Owner receives and reads message
    console.log('\n2. Property owner checks messages...');
    const ownerMessagesResponse = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${ownerToken}` }
    });
    console.log(`✅ Owner has ${ownerMessagesResponse.data.data?.length || 0} messages`);
    
    const messageId = ownerMessagesResponse.data.data?.[0]?._id || ownerMessagesResponse.data.data?.[0]?.id;
    
    if (messageId) {
      await delay(500);
      
      // Step 3: Owner marks message as read
      console.log('\n3. Owner marks message as read...');
      const markReadResponse = await axios.put(`${BASE_URL}/messages/${messageId}/read`, {}, {
        headers: { Authorization: `Bearer ${ownerToken}` }
      });
      console.log('✅ Message marked as read');
      
      await delay(1000);
      
      // Step 4: Owner replies to inquiry
      console.log('\n4. Owner replies to inquiry...');
      const replyData = {
        receiverId: userId,
        propertyId: propertyId,
        subject: 'Re: Inquiry about your beautiful house',
        content: 'Thank you for your interest in my property! I would be happy to arrange a viewing. I am available this weekend. Please let me know what time works best for you. Looking forward to hearing from you!'
      };
      
      const replyResponse = await axios.post(`${BASE_URL}/messages`, replyData, {
        headers: { Authorization: `Bearer ${ownerToken}` }
      });
      console.log('✅ Owner replied to inquiry');
    }
    
    await delay(1000);
    
    // === SCENARIO 5: ADMIN MODERATION ===
    console.log('\n\n📋 SCENARIO 5: Admin Property Moderation');
    console.log('-' .repeat(60));
    
    // Step 1: Admin logs in
    console.log('\n1. Admin login...');
    const adminLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    const adminToken = adminLoginResponse.data.data.tokens.accessToken;
    console.log('✅ Admin logged in successfully');
    
    await delay(1000);
    
    // Step 2: Admin reviews property
    console.log('\n2. Admin reviews property for approval...');
    const adminPropertiesResponse = await axios.get(`${BASE_URL}/admin/properties`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log(`✅ Admin found ${adminPropertiesResponse.data.data?.length || 0} properties to review`);
    
    await delay(1000);
    
    // Step 3: Admin approves property
    console.log('\n3. Admin approves property...');
    const approveResponse = await axios.put(`${BASE_URL}/admin/properties/${propertyId}/approve`, {}, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Property approved by admin');
    
    await delay(1000);
    
    // === SCENARIO 6: USER DASHBOARD AND PROFILE MANAGEMENT ===
    console.log('\n\n📋 SCENARIO 6: User Dashboard and Profile Management');
    console.log('-' .repeat(60));
    
    // Step 1: User checks dashboard
    console.log('\n1. User checks dashboard stats...');
    const userStatsResponse = await axios.get(`${BASE_URL}/users/dashboard/stats`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ User dashboard loaded');
    console.log('   Total Favorites:', userStatsResponse.data.data?.totalFavorites || 0);
    console.log('   Unread Messages:', userStatsResponse.data.data?.unreadMessages || 0);
    
    await delay(1000);
    
    // Step 2: User updates profile
    console.log('\n2. User updates profile...');
    const profileUpdateData = {
      name: 'Ahmad Khan (Updated)',
      bio: 'Looking for a beautiful family home in Kabul',
      location: 'Kabul, Afghanistan'
    };
    
    const updateProfileResponse = await axios.put(`${BASE_URL}/auth/profile`, profileUpdateData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Profile updated successfully');
    
    await delay(1000);
    
    // Step 3: User checks favorites
    console.log('\n3. User checks favorite properties...');
    const favoritesResponse = await axios.get(`${BASE_URL}/users/favorites`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log(`✅ User has ${favoritesResponse.data.data?.length || 0} favorite properties`);
    
    await delay(1000);
    
    // === FINAL VERIFICATION ===
    console.log('\n\n📋 FINAL SYSTEM VERIFICATION');
    console.log('-' .repeat(60));
    
    // Verify all data integrity
    console.log('\n1. Verifying data integrity...');
    
    // Check user exists
    const finalUserCheck = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ User data integrity verified');
    
    // Check property exists and is approved
    const finalPropertyCheck = await axios.get(`${BASE_URL}/properties/${propertyId}`);
    console.log('✅ Property data integrity verified');
    console.log('   Property Status: APPROVED');
    
    // Check messages exist
    const finalMessageCheck = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log(`✅ Message system verified - ${finalMessageCheck.data.data?.length || 0} messages`);
    
    // Check favorites exist
    const finalFavoriteCheck = await axios.get(`${BASE_URL}/users/favorites`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log(`✅ Favorites system verified - ${finalFavoriteCheck.data.data?.length || 0} favorites`);
    
    console.log('\n' + '=' .repeat(80));
    console.log('🎉 END-TO-END TESTING COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(80));
    
    console.log('\n📊 JOURNEY SUMMARY:');
    console.log('✅ User Registration & Authentication');
    console.log('✅ Property Creation & Management');
    console.log('✅ Property Search & Discovery');
    console.log('✅ Favorites System');
    console.log('✅ Messaging & Communication');
    console.log('✅ Admin Moderation');
    console.log('✅ User Dashboard & Profile');
    console.log('✅ Data Integrity & Persistence');
    
    console.log('\n🏆 RESULT: ALL SYSTEMS WORKING PERFECTLY!');
    
  } catch (error) {
    console.error('\n❌ End-to-end test failed:', error.response?.status, error.response?.data || error.message);
    console.error('Error occurred during user journey testing');
  }
}

testEndToEndUserJourney();
