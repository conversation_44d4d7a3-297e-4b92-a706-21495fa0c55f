const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testBasicAPI() {
  console.log('🧪 Testing Basic API Functionality...\n');
  
  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ Health Check:', healthResponse.status, healthResponse.data.status);
    
    await delay(1000);
    
    // Test 2: Get Properties (public endpoint)
    console.log('\n2. Testing Get Properties...');
    const propertiesResponse = await axios.get(`${BASE_URL}/properties`);
    console.log('✅ Get Properties:', propertiesResponse.status, `Found ${propertiesResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test 3: Search Properties
    console.log('\n3. Testing Property Search...');
    const searchResponse = await axios.get(`${BASE_URL}/properties/search?q=house`);
    console.log('✅ Property Search:', searchResponse.status, `Found ${searchResponse.data.data?.length || 0} results`);
    
    await delay(1000);
    
    // Test 4: User Registration
    console.log('\n4. Testing User Registration...');
    const testUser = {
      name: 'Test User',
      email: `test${Date.now()}@example.com`,
      password: 'TestPassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
    console.log('✅ User Registration:', registerResponse.status, 'User created successfully');
    
    const accessToken = registerResponse.data.data.tokens.accessToken;
    
    await delay(1000);
    
    // Test 5: User Login
    console.log('\n5. Testing User Login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ User Login:', loginResponse.status, 'Login successful');
    
    await delay(1000);
    
    // Test 6: Get Profile (protected endpoint)
    console.log('\n6. Testing Get Profile...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Get Profile:', profileResponse.status, `User: ${profileResponse.data.data.user.name}`);
    
    await delay(1000);
    
    // Test 7: Create Property
    console.log('\n7. Testing Property Creation...');
    const propertyData = {
      title: 'Test Property',
      description: 'A beautiful test property',
      price: 150000,
      category: 'SALE',
      type: 'HOUSE',
      province: 'Kabul',
      city: 'Kabul',
      district: 'District 1',
      address: '123 Test Street',
      area: 1500,
      bedrooms: 3,
      bathrooms: 2
    };
    
    const createPropertyResponse = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Property Creation:', createPropertyResponse.status, 'Property created successfully');
    
    const propertyId = createPropertyResponse.data.data._id || createPropertyResponse.data.data.id;
    
    await delay(1000);
    
    // Test 8: Add to Favorites
    console.log('\n8. Testing Add to Favorites...');
    const addFavoriteResponse = await axios.post(`${BASE_URL}/users/favorites/${propertyId}`, {}, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Add to Favorites:', addFavoriteResponse.status, 'Added to favorites');
    
    await delay(1000);
    
    // Test 9: Get Favorites
    console.log('\n9. Testing Get Favorites...');
    const getFavoritesResponse = await axios.get(`${BASE_URL}/users/favorites`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Get Favorites:', getFavoritesResponse.status, `Found ${getFavoritesResponse.data.data?.length || 0} favorites`);
    
    console.log('\n🎉 All basic API tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.status, error.response?.data || error.message);
  }
}

// Wait a bit for rate limits to reset, then run tests
console.log('⏳ Waiting for rate limits to reset...');
setTimeout(testBasicAPI, 5000);
