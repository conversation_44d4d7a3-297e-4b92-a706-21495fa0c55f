import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  propertyStep1Schema,
  propertyStep2Schema,
  propertyStep4Schema
} from '../../utils/validation';
import type {
  PropertyStep1FormData,
  PropertyStep2FormData,
  PropertyStep4FormData
} from '../../types/forms';
import { showErrorToast, showSuccessToast } from '../../utils/toast';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import { useConfig } from '../../hooks/useConfig';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  HomeIcon,
  MapPinIcon,
  PhotoIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  SparklesIcon,
  TruckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import SuccessModal from '../../components/common/SuccessModal';

interface PropertyFormData {
  // Step 1: Basic Info
  title: string;
  propertyType: string;
  category: string;
  province: string;
  city: string;
  district: string;
  address: string;
  
  // Step 2: Details
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  yearBuilt: number;
  floorNumber: number;
  totalFloors: number;
  parking: number;
  garden: boolean;
  balcony: boolean;
  furnished: string;
  features: string[];
  description: string;
  
  // Step 3: Photos
  images: File[];
  mainImageIndex: number;
  
  // Step 4: Pricing & Contact
  price: number;
  priceType: string;
  negotiable: boolean;
  contactName: string;
  phone: string;
  whatsapp: string;
  email: string;
  preferredContact: string;
  availableHours: string;
  
  // Step 5: Options
  featured: boolean;
  urgent: boolean;
  privateListing: boolean;
}

const PostPropertyPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const {
    propertyTypes: configPropertyTypes,
    propertyCategories: configPropertyCategories,
    provinces: configProvinces,
    propertyFeatures: configPropertyFeatures,
    getCitiesByProvince,
    getDistrictsByCity,
    loading: configLoading
  } = useConfig();
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    propertyType: '',
    category: '',
    province: '',
    city: '',
    district: '',
    address: '',
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    areaUnit: 'sq ft',
    yearBuilt: new Date().getFullYear(),
    floorNumber: 1,
    totalFloors: 1,
    parking: 0,
    garden: false,
    balcony: false,
    furnished: 'no',
    features: [],
    description: '',
    images: [],
    mainImageIndex: 0,
    price: 0,
    priceType: 'total',
    negotiable: false,
    contactName: '',
    phone: '',
    whatsapp: '',
    email: '',
    preferredContact: 'phone',
    availableHours: '9am-6pm',
    featured: false,
    urgent: false,
    privateListing: false
  });

  // Form validation for each step
  const step1Form = useForm<PropertyStep1FormData>({
    resolver: zodResolver(propertyStep1Schema),
    mode: 'onBlur',
    defaultValues: {
      title: '',
      propertyType: '',
      category: '',
      province: '',
      city: '',
      district: '',
      address: ''
    }
  });

  const step2Form = useForm<PropertyStep2FormData>({
    resolver: zodResolver(propertyStep2Schema),
    mode: 'onBlur',
    defaultValues: {
      bedrooms: 0,
      bathrooms: 0,
      area: 0,
      description: ''
    }
  });

  const step4Form = useForm<PropertyStep4FormData>({
    resolver: zodResolver(propertyStep4Schema),
    mode: 'onBlur',
    defaultValues: {
      price: 0,
      contactName: '',
      phone: ''
    }
  });

  // Error states for each step
  const [stepErrors, setStepErrors] = useState<{[key: number]: string[]}>({});

  const totalSteps = 5;

  // Convert config data to form options
  const propertyTypes = configPropertyTypes.map(type => type.label);
  const categories = configPropertyCategories.map(category => category.label);
  const provinces = configProvinces.map(province => province.name);
  const features = configPropertyFeatures.map(feature => feature.label);

  // Get cities for selected province
  const getCitiesForProvince = (provinceName: string) => {
    const province = configProvinces.find(p => p.name === provinceName);
    return province ? province.cities.map(city => city.name) : [];
  };

  // Get districts for selected city
  const getDistrictsForCity = (provinceName: string, cityName: string) => {
    const province = configProvinces.find(p => p.name === provinceName);
    if (!province) return [];
    const city = province.cities.find(c => c.name === cityName);
    return city ? city.districts.map(district => district.name) : [];
  };

  const handleInputChange = (field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Update form validation state based on current step
    if (currentStep === 1) {
      const step1Fields = ['title', 'propertyType', 'category', 'province', 'city', 'district', 'address'];
      if (step1Fields.includes(field)) {
        step1Form.setValue(field as keyof PropertyStep1FormData, value);
        step1Form.clearErrors(field as keyof PropertyStep1FormData);
      }
    } else if (currentStep === 2) {
      const step2Fields = ['bedrooms', 'bathrooms', 'area', 'description'];
      if (step2Fields.includes(field)) {
        step2Form.setValue(field as keyof PropertyStep2FormData, value);
        step2Form.clearErrors(field as keyof PropertyStep2FormData);
      }
    } else if (currentStep === 4) {
      const step4Fields = ['price', 'contactName', 'phone'];
      if (step4Fields.includes(field)) {
        step4Form.setValue(field as keyof PropertyStep4FormData, value);
        step4Form.clearErrors(field as keyof PropertyStep4FormData);
      }
    }
  };

  const handleFeatureToggle = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  const handleImageUpload = (files: FileList | null) => {
    if (files) {
      const newImages = Array.from(files);
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...newImages]
      }));
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
      mainImageIndex: prev.mainImageIndex >= index ? Math.max(0, prev.mainImageIndex - 1) : prev.mainImageIndex
    }));
  };

  const setMainImage = (index: number) => {
    setFormData(prev => ({ ...prev, mainImageIndex: index }));
  };

  const validateCurrentStep = async (): Promise<boolean> => {
    let isValid = true;
    const errors: string[] = [];

    try {
      if (currentStep === 1) {
        const step1Data = {
          title: formData.title,
          propertyType: formData.propertyType,
          category: formData.category,
          province: formData.province,
          city: formData.city,
          district: formData.district,
          address: formData.address
        };

        await propertyStep1Schema.parseAsync(step1Data);
        step1Form.clearErrors();
      } else if (currentStep === 2) {
        const step2Data = {
          bedrooms: formData.bedrooms,
          bathrooms: formData.bathrooms,
          area: formData.area,
          description: formData.description
        };

        await propertyStep2Schema.parseAsync(step2Data);
        step2Form.clearErrors();
      } else if (currentStep === 4) {
        const step4Data = {
          price: formData.price,
          contactName: formData.contactName,
          phone: formData.phone
        };

        await propertyStep4Schema.parseAsync(step4Data);
        step4Form.clearErrors();
      }
    } catch (error: any) {
      isValid = false;
      if (error.errors) {
        error.errors.forEach((err: any) => {
          errors.push(err.message);

          // Set form errors for visual feedback
          if (currentStep === 1) {
            step1Form.setError(err.path[0] as keyof PropertyStep1FormData, {
              message: err.message
            });
          } else if (currentStep === 2) {
            step2Form.setError(err.path[0] as keyof PropertyStep2FormData, {
              message: err.message
            });
          } else if (currentStep === 4) {
            step4Form.setError(err.path[0] as keyof PropertyStep4FormData, {
              message: err.message
            });
          }
        });
      }

      // Show error toast with all validation messages
      const errorMessage = errors.length > 1
        ? `Please fix the following errors:\n• ${errors.join('\n• ')}`
        : errors[0] || 'Please fill in all required fields';

      showErrorToast(errorMessage);
    }

    // Update step errors state
    setStepErrors(prev => ({
      ...prev,
      [currentStep]: errors
    }));

    return isValid;
  };

  const nextStep = async () => {
    const isValid = await validateCurrentStep();

    if (isValid && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      showSuccessToast(`Step ${currentStep} completed successfully!`);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Check authentication first
    if (!isAuthenticated) {
      showErrorToast('Please sign in to post a property');
      navigate('/login');
      return;
    }

    try {
      setIsSubmitting(true);

      // Create FormData for API submission
      const formDataToSubmit = new FormData();

      // Add basic property data
      formDataToSubmit.append('title', formData.title);
      formDataToSubmit.append('description', formData.description);
      formDataToSubmit.append('price', formData.price.toString());
      formDataToSubmit.append('category', formData.category === 'For Sale' ? 'SALE' : 'RENT');
      formDataToSubmit.append('type', formData.propertyType.toUpperCase());
      formDataToSubmit.append('province', formData.province);
      formDataToSubmit.append('city', formData.city);
      formDataToSubmit.append('district', formData.district);
      formDataToSubmit.append('address', formData.address);
      formDataToSubmit.append('area', formData.area.toString());
      formDataToSubmit.append('bedrooms', formData.bedrooms.toString());
      formDataToSubmit.append('bathrooms', formData.bathrooms.toString());
      formDataToSubmit.append('parking', formData.parking > 0 ? 'true' : 'false');
      formDataToSubmit.append('garden', formData.garden.toString());
      formDataToSubmit.append('balcony', formData.balcony.toString());
      formDataToSubmit.append('furnished', formData.furnished === 'yes' ? 'true' : 'false');

      // Add features as JSON string
      if (formData.features.length > 0) {
        formDataToSubmit.append('features', JSON.stringify(formData.features));
      }

      // Add images
      formData.images.forEach((image, index) => {
        formDataToSubmit.append('images', image);
      });

      // Submit to API
      const response = await apiService.createProperty(formDataToSubmit);

      showSuccessToast('Property created successfully!');
      setShowSuccessModal(true);

    } catch (error: any) {
      console.error('Property creation error:', error);

      if (error.response?.status === 401) {
        showErrorToast('Please sign in to post a property');
        navigate('/login');
      } else {
        const message = error.response?.data?.message || 'Failed to create property. Please try again.';
        showErrorToast(message);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepTitle = (step: number) => {
    const titles = [
      'Basic Information',
      'Property Details',
      'Photos & Media',
      'Pricing & Contact',
      'Review & Publish'
    ];
    return titles[step - 1];
  };

  const getStepIcon = (step: number) => {
    const icons = [HomeIcon, DocumentTextIcon, PhotoIcon, CurrencyDollarIcon, CheckCircleIcon];
    return icons[step - 1];
  };

  // Show authentication prompt if user is not logged in
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <ExclamationTriangleIcon className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Sign In Required</h2>
          <p className="text-gray-600 mb-6">
            You need to be signed in to post a property. Please sign in or create an account to continue.
          </p>
          <div className="space-y-3">
            <Link to="/login" className="btn-primary w-full">
              Sign In
            </Link>
            <Link to="/register" className="btn-outline w-full">
              Create Account
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                Post Your Property
              </h1>
              <p className="text-gray-600 mt-1">
                List your property for free and reach thousands of potential buyers
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="btn-ghost">
                Save as Draft
              </button>
              <Link to="/listings" className="btn-outline">
                Cancel
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3, 4, 5].map((step) => {
              const Icon = getStepIcon(step);
              return (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    step < currentStep 
                      ? 'bg-secondary-500 border-secondary-500 text-white' 
                      : step === currentStep
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                    {step < currentStep ? (
                      <CheckCircleIcon className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  {step < totalSteps && (
                    <div className={`w-16 lg:w-24 h-1 mx-2 transition-all duration-300 ${
                      step < currentStep ? 'bg-secondary-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
          
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Step {currentStep} of {totalSteps}: {getStepTitle(currentStep)}
            </h2>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
              <div 
                className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-white rounded-xl shadow-soft p-6 lg:p-8">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-6 animate-fade-in">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="e.g., Beautiful 3-bedroom villa in Wazir Akbar Khan"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.title
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step1Form.formState.errors.title && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.title.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Type *
                  </label>
                  <select
                    value={formData.propertyType}
                    onChange={(e) => handleInputChange('propertyType', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.propertyType
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Type</option>
                    {propertyTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {step1Form.formState.errors.propertyType && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.propertyType.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.category
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Category</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  {step1Form.formState.errors.category && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.category.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Province *
                  </label>
                  <select
                    value={formData.province}
                    onChange={(e) => handleInputChange('province', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.province
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select Province</option>
                    {provinces.map(province => (
                      <option key={province} value={province}>{province}</option>
                    ))}
                  </select>
                  {step1Form.formState.errors.province && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.province.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City *
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="Enter city name"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.city
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step1Form.formState.errors.city && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.city.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    District *
                  </label>
                  <input
                    type="text"
                    value={formData.district}
                    onChange={(e) => handleInputChange('district', e.target.value)}
                    placeholder="Enter district name"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.district
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step1Form.formState.errors.district && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.district.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="Enter street address"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step1Form.formState.errors.address
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step1Form.formState.errors.address && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step1Form.formState.errors.address.message}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Property Details */}
          {currentStep === 2 && (
            <div className="space-y-6 animate-fade-in">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bedrooms *
                  </label>
                  <select
                    value={formData.bedrooms}
                    onChange={(e) => handleInputChange('bedrooms', parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                      <option key={num} value={num}>{num === 0 ? 'Studio' : `${num} Bedroom${num > 1 ? 's' : ''}`}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bathrooms *
                  </label>
                  <select
                    value={formData.bathrooms}
                    onChange={(e) => handleInputChange('bathrooms', parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                      <option key={num} value={num}>{num} Bathroom{num > 1 ? 's' : ''}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Parking Spaces
                  </label>
                  <select
                    value={formData.parking}
                    onChange={(e) => handleInputChange('parking', parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    {[0, 1, 2, 3, 4, 5].map(num => (
                      <option key={num} value={num}>{num === 0 ? 'No Parking' : `${num} Space${num > 1 ? 's' : ''}`}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Area *
                  </label>
                  <div className="flex">
                    <input
                      type="number"
                      value={formData.area || ''}
                      onChange={(e) => handleInputChange('area', parseInt(e.target.value) || 0)}
                      placeholder="Enter area"
                      className={`flex-1 px-4 py-3 border rounded-l-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                        step2Form.formState.errors.area
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-300'
                      }`}
                    />
                    <select
                      value={formData.areaUnit}
                      onChange={(e) => handleInputChange('areaUnit', e.target.value)}
                      className="px-4 py-3 border border-l-0 border-gray-300 rounded-r-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                    >
                      <option value="sq ft">sq ft</option>
                      <option value="sq m">sq m</option>
                      <option value="marla">marla</option>
                      <option value="kanal">kanal</option>
                    </select>
                  </div>
                  {step2Form.formState.errors.area && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step2Form.formState.errors.area.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Year Built
                  </label>
                  <input
                    type="number"
                    value={formData.yearBuilt}
                    onChange={(e) => handleInputChange('yearBuilt', parseInt(e.target.value))}
                    min="1900"
                    max={new Date().getFullYear()}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Furnished
                  </label>
                  <select
                    value={formData.furnished}
                    onChange={(e) => handleInputChange('furnished', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="no">Unfurnished</option>
                    <option value="partial">Partially Furnished</option>
                    <option value="full">Fully Furnished</option>
                  </select>
                </div>

                <div className="flex items-center space-x-6">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.garden}
                      onChange={(e) => handleInputChange('garden', e.target.checked)}
                      className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                    />
                    <span className="ml-2 text-sm text-gray-700">Garden</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.balcony}
                      onChange={(e) => handleInputChange('balcony', e.target.checked)}
                      className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                    />
                    <span className="ml-2 text-sm text-gray-700">Balcony</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Features & Amenities
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {features.map(feature => (
                    <label key={feature} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.includes(feature)}
                        onChange={() => handleFeatureToggle(feature)}
                        className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                      />
                      <span className="ml-2 text-sm text-gray-700">{feature}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  placeholder="Describe your property in detail..."
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                    step2Form.formState.errors.description
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300'
                  }`}
                />
                {step2Form.formState.errors.description && (
                  <div className="flex items-center mt-2 text-red-600 text-sm">
                    <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                    {step2Form.formState.errors.description.message}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Photos & Media */}
          {currentStep === 3 && (
            <div className="space-y-6 animate-fade-in">
              <div className="text-center">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Add Photos of Your Property
                </h3>
                <p className="text-gray-600 mb-6">
                  Upload high-quality photos to attract more buyers. The first photo will be your main image.
                </p>
              </div>

              {/* Image Upload Area */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors duration-300">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e.target.files)}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload" className="cursor-pointer">
                  <PlusIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-lg font-medium text-gray-900 mb-2">
                    Click to upload photos
                  </div>
                  <div className="text-sm text-gray-500">
                    PNG, JPG, GIF up to 10MB each
                  </div>
                </label>
              </div>

              {/* Uploaded Images */}
              {formData.images.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">
                    Uploaded Photos ({formData.images.length})
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Property ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />

                        {/* Main Image Badge */}
                        {index === formData.mainImageIndex && (
                          <div className="absolute top-2 left-2 bg-primary-500 text-white px-2 py-1 rounded text-xs font-medium">
                            Main Photo
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {index !== formData.mainImageIndex && (
                            <button
                              onClick={() => setMainImage(index)}
                              className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300"
                              title="Set as main photo"
                            >
                              <EyeIcon className="w-4 h-4 text-gray-600" />
                            </button>
                          )}
                          <button
                            onClick={() => removeImage(index)}
                            className="w-8 h-8 bg-red-500/90 rounded-full flex items-center justify-center hover:bg-red-500 transition-colors duration-300"
                            title="Remove photo"
                          >
                            <TrashIcon className="w-4 h-4 text-white" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Pricing & Contact */}
          {currentStep === 4 && (
            <div className="space-y-6 animate-fade-in">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price *
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <input
                      type="number"
                      value={formData.price || ''}
                      onChange={(e) => handleInputChange('price', parseInt(e.target.value) || 0)}
                      placeholder="Enter price"
                      className={`w-full pl-8 pr-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                        step4Form.formState.errors.price
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-300'
                      }`}
                    />
                  </div>
                  {step4Form.formState.errors.price && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step4Form.formState.errors.price.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price Type
                  </label>
                  <select
                    value={formData.priceType}
                    onChange={(e) => handleInputChange('priceType', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="total">Total Price</option>
                    <option value="monthly">Per Month</option>
                    <option value="yearly">Per Year</option>
                    <option value="sqft">Per Sq Ft</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.negotiable}
                    onChange={(e) => handleInputChange('negotiable', e.target.checked)}
                    className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                  />
                  <span className="ml-2 text-sm text-gray-700">Price is negotiable</span>
                </label>
              </div>

              <hr className="my-8" />

              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Name *
                  </label>
                  <input
                    type="text"
                    value={formData.contactName}
                    onChange={(e) => handleInputChange('contactName', e.target.value)}
                    placeholder="Enter your name"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step4Form.formState.errors.contactName
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step4Form.formState.errors.contactName && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step4Form.formState.errors.contactName.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+93 70 123 4567"
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 ${
                      step4Form.formState.errors.phone
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300'
                    }`}
                  />
                  {step4Form.formState.errors.phone && (
                    <div className="flex items-center mt-2 text-red-600 text-sm">
                      <ExclamationTriangleIcon className="w-4 h-4 mr-1" />
                      {step4Form.formState.errors.phone.message}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    WhatsApp Number
                  </label>
                  <input
                    type="tel"
                    value={formData.whatsapp}
                    onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                    placeholder="+93 70 123 4567"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Contact Method
                  </label>
                  <select
                    value={formData.preferredContact}
                    onChange={(e) => handleInputChange('preferredContact', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="phone">Phone Call</option>
                    <option value="whatsapp">WhatsApp</option>
                    <option value="email">Email</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Available Hours
                  </label>
                  <select
                    value={formData.availableHours}
                    onChange={(e) => handleInputChange('availableHours', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="9am-6pm">9 AM - 6 PM</option>
                    <option value="8am-8pm">8 AM - 8 PM</option>
                    <option value="anytime">Anytime</option>
                    <option value="weekends">Weekends Only</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Review & Publish */}
          {currentStep === 5 && (
            <div className="space-y-6 animate-fade-in">
              <div className="text-center mb-8">
                <CheckCircleIcon className="w-16 h-16 text-secondary-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Review Your Listing
                </h3>
                <p className="text-gray-600">
                  Please review all information before publishing your property
                </p>
              </div>

              {/* Property Summary */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Property Summary</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div><span className="font-medium">Title:</span> {formData.title}</div>
                  <div><span className="font-medium">Type:</span> {formData.propertyType}</div>
                  <div><span className="font-medium">Category:</span> {formData.category}</div>
                  <div><span className="font-medium">Location:</span> {formData.city}, {formData.province}</div>
                  <div><span className="font-medium">Bedrooms:</span> {formData.bedrooms}</div>
                  <div><span className="font-medium">Bathrooms:</span> {formData.bathrooms}</div>
                  <div><span className="font-medium">Area:</span> {formData.area} {formData.areaUnit}</div>
                  <div><span className="font-medium">Price:</span> ${formData.price.toLocaleString()}</div>
                </div>
              </div>

              {/* Publishing Options */}
              <div className="bg-primary-50 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Publishing Options</h4>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.featured}
                      onChange={(e) => handleInputChange('featured', e.target.checked)}
                      className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Make this a featured listing (+$50)
                    </span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.urgent}
                      onChange={(e) => handleInputChange('urgent', e.target.checked)}
                      className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Mark as urgent listing (+$25)
                    </span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.privateListing}
                      onChange={(e) => handleInputChange('privateListing', e.target.checked)}
                      className="rounded border-gray-300 text-primary-500 focus:ring-primary-200"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Private listing (hide contact details until inquiry)
                    </span>
                  </label>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-500 focus:ring-primary-200 mt-1"
                    required
                  />
                  <div className="ml-3 text-sm">
                    <p className="text-gray-700">
                      I agree to the{' '}
                      <Link to="/terms" className="text-primary-500 hover:text-primary-600 font-medium">
                        Terms and Conditions
                      </Link>{' '}
                      and{' '}
                      <Link to="/privacy" className="text-primary-500 hover:text-primary-600 font-medium">
                        Privacy Policy
                      </Link>
                      . I confirm that all information provided is accurate and I have the right to list this property.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between mt-8">
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
              currentStep === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <ChevronLeftIcon className="w-5 h-5" />
            <span>Previous</span>
          </button>

          <div className="text-sm text-gray-500">
            Step {currentStep} of {totalSteps}
          </div>

          {currentStep < totalSteps ? (
            <button
              onClick={nextStep}
              className="btn-primary flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRightIcon className="w-5 h-5" />
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Publishing...</span>
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-5 h-5" />
                  <span>Publish Property</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Property Listed Successfully!"
        message="Your property has been submitted for review. It will be published within 24 hours after approval. You can track the status in your dashboard."
        propertyId={123} // In real app, this would be the actual property ID from backend
      />
    </div>
  );
};

export default PostPropertyPage;
