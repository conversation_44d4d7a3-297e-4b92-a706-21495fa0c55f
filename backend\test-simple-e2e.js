const axios = require("axios");

const BASE_URL = "http://localhost:3001/api";

async function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function testSimpleEndToEnd() {
  console.log("🚀 Simple End-to-End User Journey Test...\n");

  try {
    // === USER REGISTRATION ===
    console.log("1. 👤 User Registration...");
    const user = {
      name: "Test User E2E",
      email: `e2e.user.${Date.now()}@example.com`,
      password: "TestPassword123!",
      phone: `+9370${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(7, "0")}`,
    };

    const registerResponse = await axios.post(
      `${BASE_URL}/auth/register`,
      user
    );
    const userToken = registerResponse.data.data.tokens.accessToken;
    const userId = registerResponse.data.data.user.id;
    console.log("✅ User registered:", user.name);

    await delay(1000);

    // === PROPERTY CREATION ===
    console.log("\n2. 🏠 Property Creation...");
    const property = {
      title: "E2E Test Property",
      description: "A property created for end-to-end testing",
      price: 200000,
      category: "SALE",
      type: "HOUSE",
      province: "Kabul",
      city: "Kabul",
      district: "District 1",
      address: "123 E2E Test Street",
      area: 1800,
      bedrooms: 3,
      bathrooms: 2,
    };

    const createPropertyResponse = await axios.post(
      `${BASE_URL}/properties`,
      property,
      {
        headers: { Authorization: `Bearer ${userToken}` },
      }
    );
    const propertyId =
      createPropertyResponse.data.data._id ||
      createPropertyResponse.data.data.id;
    console.log("✅ Property created:", property.title);

    await delay(1000);

    // === PROPERTY SEARCH ===
    console.log("\n3. 🔍 Property Search...");
    const searchResponse = await axios.get(
      `${BASE_URL}/properties/search?q=E2E`
    );
    console.log(
      `✅ Search found ${searchResponse.data.data?.length || 0} properties`
    );

    await delay(1000);

    // === CREATE SECOND USER ===
    console.log("\n4. 👥 Second User Registration...");
    const user2 = {
      name: "Second User E2E",
      email: `e2e.user2.${Date.now()}@example.com`,
      password: "TestPassword123!",
      phone: `+9370${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(7, "0")}`,
    };

    const register2Response = await axios.post(
      `${BASE_URL}/auth/register`,
      user2
    );
    const user2Token = register2Response.data.data.tokens.accessToken;
    const user2Id = register2Response.data.data.user.id;
    console.log("✅ Second user registered:", user2.name);

    await delay(1000);

    // === FAVORITES SYSTEM ===
    console.log("\n5. ❤️ Favorites System...");
    const addFavoriteResponse = await axios.post(
      `${BASE_URL}/users/favorites/${propertyId}`,
      {},
      {
        headers: { Authorization: `Bearer ${user2Token}` },
      }
    );
    console.log("✅ Property added to favorites");

    const getFavoritesResponse = await axios.get(
      `${BASE_URL}/users/favorites`,
      {
        headers: { Authorization: `Bearer ${user2Token}` },
      }
    );
    console.log(
      `✅ User has ${getFavoritesResponse.data.data?.length || 0} favorites`
    );

    await delay(1000);

    // === MESSAGING SYSTEM ===
    console.log("\n6. 💬 Messaging System...");
    const messageData = {
      receiverId: userId,
      propertyId: propertyId,
      subject: "E2E Test Message",
      content: "This is a test message for end-to-end testing",
    };

    const sendMessageResponse = await axios.post(
      `${BASE_URL}/messages`,
      messageData,
      {
        headers: { Authorization: `Bearer ${user2Token}` },
      }
    );
    console.log("✅ Message sent");

    const getMessagesResponse = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${userId}` },
    });
    console.log(
      `✅ User has ${getMessagesResponse.data.data?.length || 0} messages`
    );

    await delay(1000);

    // === USER DASHBOARD ===
    console.log("\n7. 📊 User Dashboard...");
    const dashboardResponse = await axios.get(
      `${BASE_URL}/users/dashboard/stats`,
      {
        headers: { Authorization: `Bearer ${userId}` },
      }
    );
    console.log("✅ Dashboard stats loaded");
    console.log(
      "   Properties:",
      dashboardResponse.data.data?.totalProperties || 0
    );
    console.log(
      "   Messages:",
      dashboardResponse.data.data?.unreadMessages || 0
    );

    await delay(1000);

    // === ADMIN OPERATIONS ===
    console.log("\n8. 👑 Admin Operations...");
    const adminLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: "<EMAIL>",
      password: "admin123456",
    });
    const adminToken = adminLoginResponse.data.data.tokens.accessToken;
    console.log("✅ Admin logged in");

    const adminStatsResponse = await axios.get(`${BASE_URL}/admin/dashboard`, {
      headers: { Authorization: `Bearer ${adminToken}` },
    });
    console.log("✅ Admin dashboard loaded");

    const approveResponse = await axios.put(
      `${BASE_URL}/admin/properties/${propertyId}/approve`,
      {},
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Property approved by admin");

    await delay(1000);

    // === PROFILE MANAGEMENT ===
    console.log("\n9. 👤 Profile Management...");
    const updateProfileResponse = await axios.put(
      `${BASE_URL}/auth/profile`,
      {
        name: "Updated E2E User",
        bio: "Updated bio for testing",
      },
      {
        headers: { Authorization: `Bearer ${userToken}` },
      }
    );
    console.log("✅ Profile updated");

    await delay(1000);

    // === PROPERTY MANAGEMENT ===
    console.log("\n10. 🏠 Property Management...");
    const updatePropertyResponse = await axios.put(
      `${BASE_URL}/properties/${propertyId}`,
      {
        title: "Updated E2E Test Property",
        price: 220000,
      },
      {
        headers: { Authorization: `Bearer ${userToken}` },
      }
    );
    console.log("✅ Property updated");

    const userPropertiesResponse = await axios.get(
      `${BASE_URL}/properties/user/my-properties`,
      {
        headers: { Authorization: `Bearer ${userToken}` },
      }
    );
    console.log(
      `✅ User has ${userPropertiesResponse.data.data?.length || 0} properties`
    );

    await delay(1000);

    // === FINAL VERIFICATION ===
    console.log("\n11. ✅ Final Verification...");

    // Verify user profile
    const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${userToken}` },
    });
    console.log(
      "✅ User profile verified:",
      profileResponse.data.data.user.name
    );

    // Verify property exists
    const allPropertiesResponse = await axios.get(`${BASE_URL}/properties`);
    const foundProperty = allPropertiesResponse.data.data?.find(
      (p) => p._id === propertyId || p.id === propertyId
    );
    console.log(
      "✅ Property exists in listings:",
      foundProperty ? "Yes" : "No"
    );

    // Verify favorites
    const finalFavoritesResponse = await axios.get(
      `${BASE_URL}/users/favorites`,
      {
        headers: { Authorization: `Bearer ${user2Token}` },
      }
    );
    console.log(
      "✅ Favorites verified:",
      finalFavoritesResponse.data.data?.length || 0
    );

    // Verify messages
    const finalMessagesResponse = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${userId}` },
    });
    console.log(
      "✅ Messages verified:",
      finalMessagesResponse.data.data?.length || 0
    );

    console.log("\n" + "=".repeat(60));
    console.log("🎉 SIMPLE END-TO-END TEST COMPLETED SUCCESSFULLY!");
    console.log("=".repeat(60));

    console.log("\n📊 TEST SUMMARY:");
    console.log("✅ User Registration & Authentication");
    console.log("✅ Property Creation & Management");
    console.log("✅ Property Search & Filtering");
    console.log("✅ Favorites System");
    console.log("✅ Messaging System");
    console.log("✅ User Dashboard");
    console.log("✅ Admin Operations");
    console.log("✅ Profile Management");
    console.log("✅ Data Persistence");

    console.log("\n🏆 RESULT: ALL CORE FEATURES WORKING!");
  } catch (error) {
    console.error(
      "\n❌ Simple E2E test failed:",
      error.response?.status,
      error.response?.data || error.message
    );
  }
}

testSimpleEndToEnd();
