const axios = require("axios");

const BASE_URL = "http://localhost:3001/api";

async function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function testAdminPanel() {
  console.log("👑 Testing Admin Panel Features...\n");

  try {
    // Admin login
    console.log("1. Testing admin login...");
    const adminLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: "<EMAIL>",
      password: "admin123456",
    });
    const adminToken = adminLoginResponse.data.data.tokens.accessToken;
    const adminUser = adminLoginResponse.data.data.user;
    console.log("✅ Admin login successful");
    console.log("   Admin role:", adminUser.role);
    console.log("   Admin name:", adminUser.name);

    await delay(1000);

    // Test admin dashboard stats
    console.log("\n2. Testing admin dashboard statistics...");
    const adminStatsResponse = await axios.get(`${BASE_URL}/admin/dashboard`, {
      headers: { Authorization: `Bearer ${adminToken}` },
    });
    console.log("✅ Admin dashboard stats:", adminStatsResponse.status);
    if (adminStatsResponse.data.data) {
      console.log(
        "   Total Users:",
        adminStatsResponse.data.data.totalUsers || "N/A"
      );
      console.log(
        "   Total Properties:",
        adminStatsResponse.data.data.totalProperties || "N/A"
      );
      console.log(
        "   Total Messages:",
        adminStatsResponse.data.data.totalMessages || "N/A"
      );
      console.log(
        "   Total Reviews:",
        adminStatsResponse.data.data.totalReviews || "N/A"
      );
      console.log(
        "   Active Users:",
        adminStatsResponse.data.data.activeUsers || "N/A"
      );
      console.log(
        "   Approved Properties:",
        adminStatsResponse.data.data.approvedProperties || "N/A"
      );
      console.log(
        "   Pending Properties:",
        adminStatsResponse.data.data.pendingProperties || "N/A"
      );
    }

    await delay(1000);

    // Test get all users (admin only)
    console.log("\n3. Testing get all users (admin only)...");
    const allUsersResponse = await axios.get(`${BASE_URL}/admin/users`, {
      headers: { Authorization: `Bearer ${adminToken}` },
    });
    console.log("✅ Get all users:", allUsersResponse.status);
    console.log(`   Found ${allUsersResponse.data.data?.length || 0} users`);

    // Display some user info
    if (allUsersResponse.data.data && allUsersResponse.data.data.length > 0) {
      console.log("   Sample users:");
      allUsersResponse.data.data.slice(0, 3).forEach((user, index) => {
        console.log(
          `     ${index + 1}. ${user.name} (${user.email}) - Role: ${user.role}`
        );
      });
    }

    await delay(1000);

    // Test get all properties (admin only)
    console.log("\n4. Testing get all properties (admin only)...");
    const allPropertiesResponse = await axios.get(
      `${BASE_URL}/admin/properties`,
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Get all properties:", allPropertiesResponse.status);
    console.log(
      `   Found ${allPropertiesResponse.data.data?.length || 0} properties`
    );

    await delay(1000);

    // Create a test user to manage
    console.log("\n5. Creating test user for admin management...");
    const testUserData = {
      name: "Test User for Admin",
      email: `admin_test_${Date.now()}@example.com`,
      password: "TestPassword123!",
      phone: `+9370${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(7, "0")}`,
    };

    const testUserResponse = await axios.post(
      `${BASE_URL}/auth/register`,
      testUserData
    );
    const testUserId = testUserResponse.data.data.user.id;
    console.log("✅ Test user created for admin management");

    await delay(1000);

    // Test user management - update user status
    console.log("\n6. Testing user status management...");
    const updateUserStatusResponse = await axios.put(
      `${BASE_URL}/admin/users/${testUserId}/status`,
      {
        isActive: false,
      },
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Update user status:", updateUserStatusResponse.status);
    console.log("   User deactivated successfully");

    await delay(1000);

    // Test reactivating user
    console.log("\n7. Testing user reactivation...");
    const reactivateUserResponse = await axios.put(
      `${BASE_URL}/admin/users/${testUserId}/status`,
      {
        isActive: true,
      },
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Reactivate user:", reactivateUserResponse.status);
    console.log("   User reactivated successfully");

    await delay(1000);

    // Test property management - create a property to manage
    console.log("\n8. Creating test property for admin management...");
    const testUserToken = testUserResponse.data.data.tokens.accessToken;
    const testPropertyData = {
      title: "Admin Test Property",
      description: "A property created for admin management testing",
      price: 300000,
      category: "SALE",
      type: "HOUSE",
      province: "Kabul",
      city: "Kabul",
      district: "District 4",
      address: "999 Admin Test Street",
      area: 3000,
      bedrooms: 5,
      bathrooms: 4,
    };

    const testPropertyResponse = await axios.post(
      `${BASE_URL}/properties`,
      testPropertyData,
      {
        headers: { Authorization: `Bearer ${testUserToken}` },
      }
    );
    const testPropertyId =
      testPropertyResponse.data.data._id || testPropertyResponse.data.data.id;
    console.log("✅ Test property created for admin management");

    await delay(1000);

    // Test property approval/status management
    console.log("\n9. Testing property status management...");
    const updatePropertyStatusResponse = await axios.put(
      `${BASE_URL}/admin/properties/${testPropertyId}/approve`,
      {},
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log(
      "✅ Update property status:",
      updatePropertyStatusResponse.status
    );
    console.log("   Property approved successfully");

    await delay(1000);

    // Test filtering users by role
    console.log("\n10. Testing user filtering by role...");
    const adminUsersResponse = await axios.get(
      `${BASE_URL}/admin/users?role=ADMIN`,
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Filter users by role:", adminUsersResponse.status);
    console.log(
      `   Found ${adminUsersResponse.data.data?.length || 0} admin users`
    );

    await delay(1000);

    // Test filtering properties by status
    console.log("\n11. Testing property filtering by status...");
    const approvedPropertiesResponse = await axios.get(
      `${BASE_URL}/admin/properties?status=APPROVED`,
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log(
      "✅ Filter properties by status:",
      approvedPropertiesResponse.status
    );
    console.log(
      `   Found ${
        approvedPropertiesResponse.data.data?.length || 0
      } approved properties`
    );

    await delay(1000);

    // Test search functionality
    console.log("\n12. Testing admin search functionality...");
    const searchUsersResponse = await axios.get(
      `${BASE_URL}/admin/users?search=admin`,
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Search users:", searchUsersResponse.status);
    console.log(
      `   Found ${
        searchUsersResponse.data.data?.length || 0
      } users matching "admin"`
    );

    await delay(1000);

    // Test pagination
    console.log("\n13. Testing pagination...");
    const paginatedUsersResponse = await axios.get(
      `${BASE_URL}/admin/users?page=1&limit=5`,
      {
        headers: { Authorization: `Bearer ${adminToken}` },
      }
    );
    console.log("✅ Paginated users:", paginatedUsersResponse.status);
    if (paginatedUsersResponse.data.meta) {
      console.log("   Page:", paginatedUsersResponse.data.meta.page);
      console.log("   Limit:", paginatedUsersResponse.data.meta.limit);
      console.log("   Total:", paginatedUsersResponse.data.meta.total);
      console.log(
        "   Total Pages:",
        paginatedUsersResponse.data.meta.totalPages
      );
    }

    console.log("\n🎉 Admin panel tests completed successfully!");
  } catch (error) {
    console.error(
      "❌ Admin panel test failed:",
      error.response?.status,
      error.response?.data || error.message
    );
  }
}

testAdminPanel();
