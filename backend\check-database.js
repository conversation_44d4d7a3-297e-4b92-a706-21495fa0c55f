const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function checkDatabase() {
  console.log('🔍 Checking database contents...\n');

  try {
    // Check users (requires admin access, so we'll try to get user info after login)
    console.log('👥 Checking users...');
    
    // Try to login with one of the created users
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      
      if (loginResponse.data.success) {
        console.log('✅ User login successful - users exist in database');
        console.log(`   Logged in as: ${loginResponse.data.data.user.name}`);
        console.log(`   Role: ${loginResponse.data.data.user.role}`);
        console.log(`   Email: ${loginResponse.data.data.user.email}`);
      }
    } catch (error) {
      console.log('❌ No users found or login failed');
    }

    // Check properties
    console.log('\n🏠 Checking properties...');
    try {
      const propertiesResponse = await axios.get(`${BASE_URL}/properties`);
      if (propertiesResponse.data.success) {
        const properties = propertiesResponse.data.data;
        console.log(`✅ Found ${properties.length} properties in database`);
        
        if (properties.length > 0) {
          console.log('\n📋 Sample properties:');
          properties.slice(0, 3).forEach((property, index) => {
            console.log(`   ${index + 1}. ${property.title}`);
            console.log(`      Price: ${property.currency} ${property.price}`);
            console.log(`      Location: ${property.city}, ${property.province}`);
            console.log(`      Type: ${property.type} - ${property.category}`);
          });
        }
      }
    } catch (error) {
      console.log('❌ Failed to fetch properties:', error.response?.data?.message || error.message);
    }

    // Check API health
    console.log('\n🏥 Checking API health...');
    try {
      const healthResponse = await axios.get('http://localhost:3001/health');
      if (healthResponse.data.status === 'OK') {
        console.log('✅ API is healthy and running');
        console.log(`   Environment: ${healthResponse.data.environment}`);
        console.log(`   Timestamp: ${healthResponse.data.timestamp}`);
      }
    } catch (error) {
      console.log('❌ API health check failed');
    }

    // Test available endpoints
    console.log('\n🔗 Testing API endpoints...');
    try {
      const apiResponse = await axios.get(`${BASE_URL}`);
      if (apiResponse.data.message) {
        console.log('✅ API documentation endpoint working');
        console.log('   Available endpoints:', Object.keys(apiResponse.data.endpoints));
      }
    } catch (error) {
      console.log('❌ API documentation endpoint failed');
    }

  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  }
}

// Run the check
checkDatabase();
