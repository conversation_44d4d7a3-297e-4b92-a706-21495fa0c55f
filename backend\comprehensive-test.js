const axios = require("axios");
const fs = require("fs");
const path = require("path");

// Set higher rate limits for testing
process.env.RATE_LIMIT_MAX_REQUESTS = "1000";
process.env.RATE_LIMIT_WINDOW_MS = "60000"; // 1 minute

// Configuration
const BASE_URL = "http://localhost:3001/api";
const FRONTEND_URL = "http://localhost:5174";

// Test data
const testUser = {
  name: "Test User",
  email: `test${Date.now()}@example.com`,
  password: "TestPassword123!",
  phone: `+9370${Math.floor(Math.random() * 10000000)
    .toString()
    .padStart(7, "0")}`,
};

const testAdmin = {
  email: "<EMAIL>",
  password: "admin123456",
};

const testProperty = {
  title: "Test Property for Sale",
  description: "A beautiful test property with modern amenities",
  price: 150000,
  category: "SALE",
  type: "HOUSE",
  province: "Kabul",
  city: "Kabul",
  district: "District 1",
  address: "123 Test Street, Kabul",
  area: 1500,
  bedrooms: 3,
  bathrooms: 2,
  features: ["parking", "garden", "security"],
  amenities: ["wifi", "heating", "ac"],
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: [],
};

// Helper functions
function log(message, type = "info") {
  const timestamp = new Date().toISOString();
  const colors = {
    info: "\x1b[36m", // Cyan
    success: "\x1b[32m", // Green
    error: "\x1b[31m", // Red
    warning: "\x1b[33m", // Yellow
    reset: "\x1b[0m", // Reset
  };

  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    log(`✅ PASS: ${message}`, "success");
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    log(`❌ FAIL: ${message}`, "error");
  }
}

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
  }
}

// Test functions
async function testHealthCheck() {
  log("🏥 Testing Health Check...", "info");

  // Health check is at root level, not under /api
  const config = {
    method: "GET",
    url: "http://localhost:3001/health",
  };

  try {
    const response = await axios(config);
    const result = {
      success: true,
      data: response.data,
      status: response.status,
    };
    assert(result.success, "Health check endpoint responds");
    assert(result.status === 200, "Health check returns 200 status");
    assert(result.data?.status === "OK", "Health check returns OK status");
  } catch (error) {
    const result = {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status,
    };
    assert(result.success, "Health check endpoint responds");
    assert(result.status === 200, "Health check returns 200 status");
    assert(result.data?.status === "OK", "Health check returns OK status");
  }
}

async function testUserRegistration() {
  log("👤 Testing User Registration...", "info");

  const result = await makeRequest("POST", "/auth/register", testUser);

  if (!result.success) {
    log(`❌ Registration failed: ${JSON.stringify(result.error)}`, "error");
  }

  assert(result.success, "User registration succeeds");
  assert(result.status === 201, "Registration returns 201 status");
  assert(
    result.data?.success === true,
    "Registration response indicates success"
  );
  assert(
    result.data?.data?.user?.email === testUser.email,
    "Registered user email matches"
  );
  assert(result.data?.data?.tokens?.accessToken, "Access token is provided");

  // Store tokens for later tests
  if (result.success && result.data?.data?.tokens) {
    testUser.accessToken = result.data.data.tokens.accessToken;
    testUser.refreshToken = result.data.data.tokens.refreshToken;
    testUser.id = result.data.data.user.id;
  }
}

async function testUserLogin() {
  log("🔐 Testing User Login...", "info");

  const loginData = {
    email: testUser.email,
    password: testUser.password,
  };

  const result = await makeRequest("POST", "/auth/login", loginData);
  assert(result.success, "User login succeeds");
  assert(result.status === 200, "Login returns 200 status");
  assert(result.data?.success === true, "Login response indicates success");
  assert(
    result.data?.data?.tokens?.accessToken,
    "Access token is provided on login"
  );
}

async function testAdminLogin() {
  log("👑 Testing Admin Login...", "info");

  const result = await makeRequest("POST", "/auth/login", testAdmin);
  assert(result.success, "Admin login succeeds");
  assert(result.status === 200, "Admin login returns 200 status");
  assert(
    result.data?.success === true,
    "Admin login response indicates success"
  );

  if (result.success && result.data?.data?.tokens) {
    testAdmin.accessToken = result.data.data.tokens.accessToken;
    testAdmin.id = result.data.data.user.id;
  }
}

async function testProtectedRoute() {
  log("🔒 Testing Protected Routes...", "info");

  // Test without token
  const noTokenResult = await makeRequest("GET", "/auth/profile");
  assert(!noTokenResult.success, "Protected route fails without token");
  assert(noTokenResult.status === 401, "Returns 401 for unauthorized access");

  // Test with token
  const headers = { Authorization: `Bearer ${testUser.accessToken}` };
  const withTokenResult = await makeRequest(
    "GET",
    "/auth/profile",
    null,
    headers
  );
  assert(withTokenResult.success, "Protected route succeeds with valid token");
  assert(withTokenResult.status === 200, "Returns 200 for authorized access");
}

async function testPropertyCreation() {
  log("🏠 Testing Property Creation...", "info");

  const headers = { Authorization: `Bearer ${testUser.accessToken}` };
  const result = await makeRequest(
    "POST",
    "/properties",
    testProperty,
    headers
  );

  assert(result.success, "Property creation succeeds");
  assert(result.status === 201, "Property creation returns 201 status");
  assert(
    result.data?.success === true,
    "Property creation response indicates success"
  );
  assert(
    result.data?.data?.title === testProperty.title,
    "Created property title matches"
  );

  if (result.success && result.data?.data) {
    testProperty.id = result.data.data._id || result.data.data.id;
    log(`✅ Property created with ID: ${testProperty.id}`, "info");
  } else {
    log("❌ No property ID received from creation", "error");
  }
}

async function testPropertyRetrieval() {
  log("📋 Testing Property Retrieval...", "info");

  // Test get all properties
  const allResult = await makeRequest("GET", "/properties");
  assert(allResult.success, "Get all properties succeeds");
  assert(allResult.status === 200, "Get all properties returns 200 status");
  assert(
    Array.isArray(allResult.data?.data),
    "Properties response is an array"
  );

  // Test get single property
  if (testProperty.id) {
    log(
      `🔍 Testing single property retrieval with ID: ${testProperty.id}`,
      "info"
    );
    const singleResult = await makeRequest(
      "GET",
      `/properties/${testProperty.id}`
    );

    if (!singleResult.success) {
      log(
        `❌ Single property retrieval failed: ${JSON.stringify(
          singleResult.error
        )}`,
        "error"
      );
    }

    assert(singleResult.success, "Get single property succeeds");
    assert(
      singleResult.status === 200,
      "Get single property returns 200 status"
    );
    assert(
      singleResult.data?.data?.title === testProperty.title,
      "Retrieved property matches created property"
    );
  } else {
    log("⚠️ No property ID available for single property test", "warning");
  }
}

async function testPropertySearch() {
  log("🔍 Testing Property Search...", "info");

  // Test search by title
  const searchResult = await makeRequest(
    "GET",
    `/properties/search?q=${encodeURIComponent(testProperty.title)}`
  );
  assert(searchResult.success, "Property search succeeds");
  assert(searchResult.status === 200, "Property search returns 200 status");

  // Test filter by price range
  const filterResult = await makeRequest(
    "GET",
    `/properties?minPrice=100000&maxPrice=200000`
  );
  assert(filterResult.success, "Property filtering succeeds");
  assert(filterResult.status === 200, "Property filtering returns 200 status");
}

async function testUserProfile() {
  log("👤 Testing User Profile Management...", "info");

  const headers = { Authorization: `Bearer ${testUser.accessToken}` };

  // Test get profile
  const getResult = await makeRequest("GET", "/auth/profile", null, headers);
  assert(getResult.success, "Get user profile succeeds");
  assert(getResult.status === 200, "Get profile returns 200 status");

  // Test update profile
  const updateData = { name: "Updated User Name" };
  const updateResult = await makeRequest(
    "PUT",
    "/auth/profile",
    updateData,
    headers
  );
  assert(updateResult.success, "Update user profile succeeds");
  assert(updateResult.status === 200, "Update profile returns 200 status");
}

async function testFavorites() {
  log("❤️ Testing Favorites System...", "info");

  if (!testProperty.id) {
    log("⚠️ Skipping favorites test - no property ID available", "warning");
    return;
  }

  const headers = { Authorization: `Bearer ${testUser.accessToken}` };

  // Test add to favorites
  const addResult = await makeRequest(
    "POST",
    `/users/favorites/${testProperty.id}`,
    null,
    headers
  );
  assert(addResult.success, "Add to favorites succeeds");
  assert(addResult.status === 201, "Add to favorites returns 201 status");

  // Test get favorites
  const getResult = await makeRequest("GET", "/users/favorites", null, headers);
  assert(getResult.success, "Get favorites succeeds");
  assert(getResult.status === 200, "Get favorites returns 200 status");

  // Test remove from favorites
  const removeResult = await makeRequest(
    "DELETE",
    `/users/favorites/${testProperty.id}`,
    null,
    headers
  );
  assert(removeResult.success, "Remove from favorites succeeds");
  assert(
    removeResult.status === 200,
    "Remove from favorites returns 200 status"
  );
}

// Main test runner
async function runAllTests() {
  log("🚀 Starting Comprehensive API Testing...", "info");
  log("=".repeat(60), "info");

  try {
    await testHealthCheck();
    await delay(500);
    await testUserRegistration();
    await delay(500);
    await testUserLogin();
    await delay(500);
    await testAdminLogin();
    await delay(500);
    await testProtectedRoute();
    await delay(500);
    await testPropertyCreation();
    await delay(500);
    await testPropertyRetrieval();
    await delay(500);
    await testPropertySearch();
    await delay(500);
    await testUserProfile();
    await delay(500);
    await testFavorites();

    // Print summary
    log("=".repeat(60), "info");
    log("📊 TEST SUMMARY", "info");
    log(`✅ Passed: ${testResults.passed}`, "success");
    log(
      `❌ Failed: ${testResults.failed}`,
      testResults.failed > 0 ? "error" : "success"
    );

    if (testResults.errors.length > 0) {
      log("\n🚨 FAILED TESTS:", "error");
      testResults.errors.forEach((error) => log(`  - ${error}`, "error"));
    }

    if (testResults.failed === 0) {
      log("\n🎉 ALL TESTS PASSED! The API is working correctly.", "success");
    } else {
      log("\n⚠️ Some tests failed. Please check the errors above.", "warning");
    }
  } catch (error) {
    log(`💥 Test runner error: ${error.message}`, "error");
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testResults };
