const axios = require("axios");

const BASE_URL = "http://localhost:3001/api";

async function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function testMessagingSystem() {
  console.log("💬 Testing Messaging System...\n");

  try {
    // Create two test users
    console.log("1. Creating test users...");

    const user1Data = {
      name: "User One",
      email: `user1_${Date.now()}@example.com`,
      password: "TestPassword123!",
      phone: `+9370${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(7, "0")}`,
    };

    const user2Data = {
      name: "User Two",
      email: `user2_${Date.now()}@example.com`,
      password: "TestPassword123!",
      phone: `+9370${Math.floor(Math.random() * 10000000)
        .toString()
        .padStart(7, "0")}`,
    };

    const user1Response = await axios.post(
      `${BASE_URL}/auth/register`,
      user1Data
    );
    const user1Token = user1Response.data.data.tokens.accessToken;
    const user1Id = user1Response.data.data.user.id;
    console.log("✅ User 1 created:", user1Data.name);

    await delay(1000);

    const user2Response = await axios.post(
      `${BASE_URL}/auth/register`,
      user2Data
    );
    const user2Token = user2Response.data.data.tokens.accessToken;
    const user2Id = user2Response.data.data.user.id;
    console.log("✅ User 2 created:", user2Data.name);

    await delay(1000);

    // Create a property for messaging context
    console.log("\n2. Creating test property...");
    const propertyData = {
      title: "Test Property for Messaging",
      description: "A property to test messaging functionality",
      price: 200000,
      category: "SALE",
      type: "HOUSE",
      province: "Kabul",
      city: "Kabul",
      district: "District 1",
      address: "456 Message Test Street",
      area: 2000,
      bedrooms: 4,
      bathrooms: 3,
    };

    const propertyResponse = await axios.post(
      `${BASE_URL}/properties`,
      propertyData,
      {
        headers: { Authorization: `Bearer ${user1Token}` },
      }
    );
    const propertyId =
      propertyResponse.data.data._id || propertyResponse.data.data.id;
    console.log("✅ Property created by User 1");

    await delay(1000);

    // Test sending a message from User 2 to User 1 about the property
    console.log("\n3. Testing property inquiry message...");
    const messageData = {
      receiverId: user1Id,
      propertyId: propertyId,
      subject: "Inquiry about your property",
      content:
        "Hi, I am interested in your property. Can we discuss the details?",
    };

    const sendMessageResponse = await axios.post(
      `${BASE_URL}/messages`,
      messageData,
      {
        headers: { Authorization: `Bearer ${user2Token}` },
      }
    );
    console.log(
      "✅ Message sent from User 2 to User 1:",
      sendMessageResponse.status
    );

    await delay(1000);

    // Test getting messages for User 1 (recipient)
    console.log("\n4. Testing get messages for recipient...");
    const user1MessagesResponse = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${user1Token}` },
    });
    console.log(
      "✅ User 1 messages retrieved:",
      user1MessagesResponse.status,
      `Found ${user1MessagesResponse.data.data?.length || 0} messages`
    );

    await delay(1000);

    // Test getting messages for User 2 (sender)
    console.log("\n5. Testing get messages for sender...");
    const user2MessagesResponse = await axios.get(`${BASE_URL}/messages`, {
      headers: { Authorization: `Bearer ${user2Token}` },
    });
    console.log(
      "✅ User 2 messages retrieved:",
      user2MessagesResponse.status,
      `Found ${user2MessagesResponse.data.data?.length || 0} messages`
    );

    // Get the message ID for further testing
    let messageId = null;
    if (
      user1MessagesResponse.data.data &&
      user1MessagesResponse.data.data.length > 0
    ) {
      messageId =
        user1MessagesResponse.data.data[0]._id ||
        user1MessagesResponse.data.data[0].id;
    }

    if (messageId) {
      await delay(1000);

      // Test getting a specific message
      console.log("\n6. Testing get specific message...");
      const specificMessageResponse = await axios.get(
        `${BASE_URL}/messages/${messageId}`,
        {
          headers: { Authorization: `Bearer ${user1Token}` },
        }
      );
      console.log(
        "✅ Specific message retrieved:",
        specificMessageResponse.status
      );

      await delay(1000);

      // Test replying to the message (send a new message)
      console.log("\n7. Testing reply to message...");
      const replyData = {
        receiverId: user2Id,
        propertyId: propertyId,
        subject: "Re: Inquiry about your property",
        content:
          "Thank you for your interest! I would be happy to discuss the details. When would be a good time for you?",
      };

      const replyResponse = await axios.post(
        `${BASE_URL}/messages`,
        replyData,
        {
          headers: { Authorization: `Bearer ${user1Token}` },
        }
      );
      console.log("✅ Reply sent:", replyResponse.status);

      await delay(1000);

      // Test marking message as read
      console.log("\n8. Testing mark message as read...");
      const markReadResponse = await axios.put(
        `${BASE_URL}/messages/${messageId}/read`,
        {},
        {
          headers: { Authorization: `Bearer ${user1Token}` },
        }
      );
      console.log("✅ Message marked as read:", markReadResponse.status);
    }

    console.log("\n🎉 Messaging system tests completed successfully!");
  } catch (error) {
    console.error(
      "❌ Messaging test failed:",
      error.response?.status,
      error.response?.data || error.message
    );
  }
}

testMessagingSystem();
