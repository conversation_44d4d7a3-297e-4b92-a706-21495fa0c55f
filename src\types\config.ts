/**
 * Configuration Types
 * Shared type definitions for configuration data
 */

export interface PropertyType {
  id: string;
  name: string;
  label: string;
  icon?: string;
  description?: string;
  isActive: boolean;
}

export interface PropertyCategory {
  id: string;
  name: string;
  label: string;
  icon?: string;
  description?: string;
  isActive: boolean;
}

export interface District {
  id: string;
  name: string;
  cityId: string;
  isActive: boolean;
}

export interface City {
  id: string;
  name: string;
  provinceId: string;
  districts: District[];
  isActive: boolean;
}

export interface Province {
  id: string;
  name: string;
  code: string;
  cities: City[];
  isActive: boolean;
}

export interface PropertyFeature {
  id: string;
  name: string;
  label: string;
  icon?: string;
  category: 'amenity' | 'feature' | 'utility';
  isActive: boolean;
}

export interface Currency {
  id: string;
  code: string;
  name: string;
  symbol: string;
  exchangeRate: number;
  isDefault: boolean;
  isActive: boolean;
}

export interface SystemStats {
  totalProperties: number;
  totalUsers: number;
  totalCities: number;
  yearsExperience: number;
  lastUpdated: string;
}
