import axios from "axios";

async function testFrontend() {
  console.log("🧪 Testing frontend connectivity...\n");

  try {
    // Test if frontend is running
    console.log("🌐 Checking frontend server...");
    try {
      const frontendResponse = await axios.get("http://localhost:5174", {
        timeout: 5000,
        headers: {
          "User-Agent": "Test Script",
        },
      });

      if (frontendResponse.status === 200) {
        console.log("✅ Frontend server is running on http://localhost:5174");
      }
    } catch (error) {
      console.log("❌ Frontend server not accessible:", error.message);
    }

    // Test backend connectivity
    console.log("\n🔧 Checking backend server...");
    try {
      const backendResponse = await axios.get("http://localhost:3001/health", {
        timeout: 5000,
      });

      if (backendResponse.data.status === "OK") {
        console.log("✅ Backend server is healthy");
        console.log(`   Environment: ${backendResponse.data.environment}`);
      }
    } catch (error) {
      console.log("❌ Backend server not accessible:", error.message);
    }

    // Test API endpoints
    console.log("\n📡 Testing API endpoints...");
    try {
      const apiResponse = await axios.get("http://localhost:3001/api", {
        timeout: 5000,
      });

      if (apiResponse.data.message) {
        console.log("✅ API endpoints are accessible");
      }
    } catch (error) {
      console.log("❌ API endpoints not accessible:", error.message);
    }

    // Test properties endpoint
    console.log("\n🏠 Testing properties endpoint...");
    try {
      const propertiesResponse = await axios.get(
        "http://localhost:3001/api/properties",
        {
          timeout: 5000,
        }
      );

      if (propertiesResponse.data.success) {
        console.log(
          `✅ Properties endpoint working - found ${propertiesResponse.data.data.length} properties`
        );
      }
    } catch (error) {
      console.log(
        "❌ Properties endpoint failed:",
        error.response?.data?.message || error.message
      );
    }

    console.log("\n🎯 Summary:");
    console.log(
      "   - Frontend: http://localhost:5174 (should show homepage with fallback data)"
    );
    console.log("   - Backend: http://localhost:3001 (API server)");
    console.log(
      "   - Config Test: http://localhost:5174/config-test (configuration testing page)"
    );

    console.log("\n💡 If you see network errors in the browser:");
    console.log(
      "   1. The HomePage now uses fallback data, so it should still display properties"
    );
    console.log("   2. The ConfigTestPage shows working configuration data");
    console.log("   3. Both pages should load without blocking errors");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testFrontend();
