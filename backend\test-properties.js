const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testPropertyManagement() {
  console.log('🏠 Testing Property Management Features...\n');
  
  try {
    // Create a test user
    console.log('1. Creating test user...');
    const userData = {
      name: 'Property Owner',
      email: `owner_${Date.now()}@example.com`,
      password: 'TestPassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const userResponse = await axios.post(`${BASE_URL}/auth/register`, userData);
    const userToken = userResponse.data.data.tokens.accessToken;
    const userId = userResponse.data.data.user.id;
    console.log('✅ User created:', userData.name);
    
    await delay(1000);
    
    // Test property creation
    console.log('\n2. Testing property creation...');
    const propertyData = {
      title: 'Beautiful Modern House',
      description: 'A stunning modern house with all amenities in a prime location',
      price: 250000,
      category: 'SALE',
      type: 'HOUSE',
      province: 'Kabul',
      city: 'Kabul',
      district: 'District 2',
      address: '789 Modern Street, Kabul',
      area: 2500,
      bedrooms: 4,
      bathrooms: 3,
      features: ['parking', 'garden', 'security', 'balcony'],
      amenities: ['wifi', 'heating', 'ac', 'elevator']
    };
    
    const createResponse = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    const propertyId = createResponse.data.data._id || createResponse.data.data.id;
    console.log('✅ Property created:', createResponse.status, 'ID:', propertyId);
    
    await delay(1000);
    
    // Test get all properties
    console.log('\n3. Testing get all properties...');
    const allPropertiesResponse = await axios.get(`${BASE_URL}/properties`);
    console.log('✅ Get all properties:', allPropertiesResponse.status, `Found ${allPropertiesResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test get single property
    console.log('\n4. Testing get single property...');
    const singlePropertyResponse = await axios.get(`${BASE_URL}/properties/${propertyId}`);
    console.log('✅ Get single property:', singlePropertyResponse.status);
    console.log('   Property title:', singlePropertyResponse.data.data.title);
    console.log('   Property price:', singlePropertyResponse.data.data.price);
    
    await delay(1000);
    
    // Test property search
    console.log('\n5. Testing property search...');
    const searchResponse = await axios.get(`${BASE_URL}/properties/search?q=modern`);
    console.log('✅ Property search:', searchResponse.status, `Found ${searchResponse.data.data?.length || 0} results`);
    
    await delay(1000);
    
    // Test property filtering by price
    console.log('\n6. Testing property filtering by price...');
    const filterResponse = await axios.get(`${BASE_URL}/properties?minPrice=200000&maxPrice=300000`);
    console.log('✅ Property filter by price:', filterResponse.status, `Found ${filterResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test property filtering by location
    console.log('\n7. Testing property filtering by location...');
    const locationFilterResponse = await axios.get(`${BASE_URL}/properties?province=Kabul&city=Kabul`);
    console.log('✅ Property filter by location:', locationFilterResponse.status, `Found ${locationFilterResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test property filtering by type
    console.log('\n8. Testing property filtering by type...');
    const typeFilterResponse = await axios.get(`${BASE_URL}/properties?type=HOUSE`);
    console.log('✅ Property filter by type:', typeFilterResponse.status, `Found ${typeFilterResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test property filtering by category
    console.log('\n9. Testing property filtering by category...');
    const categoryFilterResponse = await axios.get(`${BASE_URL}/properties?category=SALE`);
    console.log('✅ Property filter by category:', categoryFilterResponse.status, `Found ${categoryFilterResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test get user's properties
    console.log('\n10. Testing get user properties...');
    const userPropertiesResponse = await axios.get(`${BASE_URL}/properties/user/my-properties`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Get user properties:', userPropertiesResponse.status, `Found ${userPropertiesResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test property update
    console.log('\n11. Testing property update...');
    const updateData = {
      title: 'Beautiful Modern House - Updated',
      price: 275000,
      description: 'A stunning modern house with all amenities in a prime location - Recently renovated'
    };
    
    const updateResponse = await axios.put(`${BASE_URL}/properties/${propertyId}`, updateData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Property updated:', updateResponse.status);
    console.log('   New title:', updateResponse.data.data.title);
    console.log('   New price:', updateResponse.data.data.price);
    
    await delay(1000);
    
    // Test property deletion
    console.log('\n12. Testing property deletion...');
    const deleteResponse = await axios.delete(`${BASE_URL}/properties/${propertyId}`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Property deleted:', deleteResponse.status);
    
    await delay(1000);
    
    // Verify property is deleted
    console.log('\n13. Verifying property deletion...');
    try {
      await axios.get(`${BASE_URL}/properties/${propertyId}`);
      console.log('❌ Property still exists after deletion');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ Property successfully deleted (404 as expected)');
      } else {
        console.log('❌ Unexpected error:', error.response?.status);
      }
    }
    
    console.log('\n🎉 Property management tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Property test failed:', error.response?.status, error.response?.data || error.message);
  }
}

testPropertyManagement();
