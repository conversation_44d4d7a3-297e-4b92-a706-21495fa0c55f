const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Sample data
const sampleUsers = [
  {
    name: '<PERSON>',
    email: 'ah<PERSON>.<EMAIL>',
    password: 'password123',
    phone: '+***********',
    role: 'USER',
    bio: 'Looking for a nice family home in Kabul.',
    location: 'Kabul, Afghanistan'
  },
  {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+***********',
    role: 'AGENT',
    bio: 'Professional real estate agent with 5 years experience.',
    location: 'Herat, Afghanistan'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+***********',
    role: 'USER',
    bio: 'Businessman looking for commercial properties.',
    location: 'Mazar-i-Sharif, Afghanistan'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+***********',
    role: 'AGENT',
    bio: 'Specialized in luxury properties and villas.',
    location: 'Kabul, Afghanistan'
  },
  {
    name: '<PERSON>',
    email: 'hassan.r<PERSON><PERSON>@example.com',
    password: 'password123',
    phone: '+***********',
    role: 'USER',
    bio: 'First-time home buyer looking for apartments.',
    location: 'Kandahar, Afghanistan'
  }
];

const sampleProperties = [
  {
    title: 'Beautiful Family House in Kabul',
    description: 'A spacious 4-bedroom house with garden and parking. Perfect for families. Located in a quiet neighborhood with easy access to schools and markets.',
    price: 85000,
    currency: 'USD',
    category: 'SALE',
    type: 'HOUSE',
    province: 'Kabul',
    city: 'Kabul',
    district: 'District 1',
    address: 'Street 15, House 45, District 1, Kabul',
    bedrooms: 4,
    bathrooms: 3,
    area: 250,
    yearBuilt: 2018,
    furnished: true,
    parking: true,
    garden: true,
    balcony: false,
    features: ['PARKING', 'GARDEN', 'SECURITY']
  },
  {
    title: 'Modern Apartment for Rent',
    description: 'Newly built 2-bedroom apartment with modern amenities. Great city views and close to business district.',
    price: 800,
    currency: 'USD',
    category: 'RENT',
    type: 'APARTMENT',
    province: 'Kabul',
    city: 'Kabul',
    district: 'District 2',
    address: 'Building 12, Floor 5, District 2, Kabul',
    bedrooms: 2,
    bathrooms: 2,
    area: 120,
    yearBuilt: 2020,
    furnished: true,
    parking: true,
    garden: false,
    balcony: true,
    features: ['PARKING', 'BALCONY', 'ELEVATOR']
  },
  {
    title: 'Luxury Villa in Herat',
    description: 'Stunning 5-bedroom villa with swimming pool and large garden. Premium location with mountain views.',
    price: 150000,
    currency: 'USD',
    category: 'SALE',
    type: 'VILLA',
    province: 'Herat',
    city: 'Herat',
    district: 'District 1',
    address: 'Villa Complex, Block A, Herat',
    bedrooms: 5,
    bathrooms: 4,
    area: 400,
    yearBuilt: 2019,
    furnished: false,
    parking: true,
    garden: true,
    balcony: true,
    features: ['PARKING', 'GARDEN', 'POOL', 'SECURITY']
  },
  {
    title: 'Commercial Office Space',
    description: 'Prime commercial space in business district. Suitable for offices, clinics, or retail. High foot traffic area.',
    price: 1200,
    currency: 'USD',
    category: 'RENT',
    type: 'COMMERCIAL',
    province: 'Kabul',
    city: 'Kabul',
    district: 'District 3',
    address: 'Business Center, Floor 2, District 3, Kabul',
    bedrooms: 0,
    bathrooms: 2,
    area: 80,
    yearBuilt: 2017,
    furnished: false,
    parking: true,
    garden: false,
    balcony: false,
    features: ['PARKING', 'ELEVATOR', 'SECURITY']
  },
  {
    title: 'Affordable Family Apartment',
    description: 'Cozy 3-bedroom apartment perfect for small families. Good neighborhood with schools and shops nearby.',
    price: 45000,
    currency: 'USD',
    category: 'SALE',
    type: 'APARTMENT',
    province: 'Mazar-i-Sharif',
    city: 'Mazar-i-Sharif',
    district: 'District 1',
    address: 'Residential Complex, Building 3, Mazar-i-Sharif',
    bedrooms: 3,
    bathrooms: 2,
    area: 140,
    yearBuilt: 2016,
    furnished: false,
    parking: false,
    garden: false,
    balcony: true,
    features: ['BALCONY']
  }
];

async function createUser(userData) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/register`, userData);
    console.log(`✅ Created user: ${userData.name} (${userData.email})`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to create user ${userData.name}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function loginUser(email, password) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, { email, password });
    console.log(`✅ Logged in user: ${email}`);
    return response.data.data.accessToken;
  } catch (error) {
    console.error(`❌ Failed to login user ${email}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function createProperty(propertyData, token) {
  try {
    const response = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ Created property: ${propertyData.title}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to create property ${propertyData.title}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function seedDatabase() {
  console.log('🌱 Starting database seeding...\n');

  try {
    // Create users
    console.log('👥 Creating users...');
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const user = await createUser(userData);
      if (user) {
        createdUsers.push({ ...userData, ...user });
      }
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(`\n✅ Created ${createdUsers.length} users\n`);

    // Create properties (assign to different users)
    console.log('🏠 Creating properties...');
    let propertyCount = 0;
    
    for (let i = 0; i < sampleProperties.length; i++) {
      const propertyData = sampleProperties[i];
      const userIndex = i % createdUsers.length; // Rotate through users
      const user = createdUsers[userIndex];
      
      if (user) {
        // Login as the user to get token
        const token = await loginUser(user.email, user.password);
        if (token) {
          const property = await createProperty(propertyData, token);
          if (property) {
            propertyCount++;
          }
        }
      }
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(`\n✅ Created ${propertyCount} properties\n`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Users: ${createdUsers.length}`);
    console.log(`   Properties: ${propertyCount}`);
    console.log('\n🔗 You can now:');
    console.log('   - Login with any of the created users');
    console.log('   - Browse the properties');
    console.log('   - Test the messaging system');
    console.log('   - Try the favorites functionality');

  } catch (error) {
    console.error('❌ Database seeding failed:', error.message);
  }
}

// Run the seeding
seedDatabase();
