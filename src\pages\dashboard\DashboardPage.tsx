import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import UserDashboard from '../../components/user/UserDashboard';
import Loading from '../../components/common/Loading';
import { apiService } from '../../services/api';
import {
  HomeIcon,
  PlusIcon,
  HeartIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  avatar: string;
}

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout, isAuthenticated, isLoading } = useAuth();
  const [dashboardStats, setDashboardStats] = useState({
    totalProperties: 0,
    activeProperties: 0,
    totalFavorites: 0,
    unreadMessages: 0,
    recentProperties: []
  });
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);

  useEffect(() => {
    // Fetch dashboard stats
    const fetchDashboardStats = async () => {
      if (user) {
        try {
          const response = await apiService.getDashboardStats();
          setDashboardStats(response.data);
        } catch (error) {
          console.error('Failed to fetch dashboard stats:', error);
        } finally {
          setStatsLoading(false);
        }
      }
    };

    fetchDashboardStats();
  }, [user]);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  if (isLoading || !user) {
    return <Loading fullScreen text="Loading dashboard..." />;
  }

  if (statsLoading) {
    return <Loading fullScreen text="Loading dashboard..." />;
  }

  // Use real dashboard data from API
  const dashboardData = {
    totalListings: dashboardStats.totalProperties || 0,
    activeListings: dashboardStats.activeProperties || 0,
    totalViews: dashboardStats.totalViews || 0,
    totalInquiries: dashboardStats.unreadMessages || 0,
    favoriteCount: dashboardStats.totalFavorites || 0,
    monthlyViews: dashboardStats.monthlyViews || 0,
    recentActivity: dashboardStats.recentActivity || []
  };

  const stats = [
    {
      label: t('dashboard.stats.myProperties'),
      value: dashboardData.totalListings.toString(),
      icon: HomeIcon,
      color: 'primary'
    },
    {
      label: t('dashboard.stats.totalViews'),
      value: dashboardData.totalViews.toLocaleString(),
      icon: EyeIcon,
      color: 'secondary'
    },
    {
      label: t('dashboard.stats.messages'),
      value: dashboardData.totalInquiries.toString(),
      icon: ChatBubbleLeftRightIcon,
      color: 'accent'
    },
    {
      label: t('dashboard.stats.savedProperties'),
      value: dashboardData.favoriteCount.toString(),
      icon: HeartIcon,
      color: 'primary'
    }
  ];

  const recentProperties = [
    {
      id: 1,
      title: 'Modern Villa in Wazir Akbar Khan',
      status: 'Active',
      views: 245,
      inquiries: 8,
      price: '$250,000',
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    },
    {
      id: 2,
      title: 'Luxury Apartment in Shahr-e-Naw',
      status: 'Pending',
      views: 156,
      inquiries: 5,
      price: '$180,000',
      image: 'https://images.unsplash.com/photo-**********-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
    }
  ];

  const quickActions = [
    { label: 'Post New Property', icon: PlusIcon, href: '/post-property', color: 'primary' },
    { label: 'Browse Properties', icon: BuildingOfficeIcon, href: '/listings', color: 'secondary' },
    { label: 'View Messages', icon: ChatBubbleLeftRightIcon, href: '/messages', color: 'accent' },
    { label: 'Account Settings', icon: Cog6ToothIcon, href: '/settings', color: 'primary' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                <HomeIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gradient">RealEstate</h1>
                <p className="text-xs text-gray-500">Dashboard</p>
              </div>
            </Link>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
                <div className="hidden sm:block">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="text-xs text-gray-500 capitalize">{user.role}</p>
                </div>
              </div>

              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                title="Logout"
              >
                <ArrowRightOnRectangleIcon className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <UserDashboard
          stats={dashboardStats}
          userName={user.name}
        />
      </div>
    </div>
  );
};

export default DashboardPage;
