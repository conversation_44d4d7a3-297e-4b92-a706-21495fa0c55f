const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testPropertyCreation() {
  console.log('🧪 Testing property creation...\n');

  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!loginResponse.data.success) {
      console.error('❌ Login failed');
      return;
    }

    const token = loginResponse.data.data.accessToken;
    console.log('✅ Login successful');

    // Test property creation with minimal data
    console.log('\n🏠 Creating test property...');
    const propertyData = {
      title: 'Test Property from Script',
      description: 'This is a test property created by the seeding script.',
      price: 50000,
      currency: 'USD',
      category: 'SALE',
      type: 'HOUSE',
      province: 'Kabul',
      city: 'Kabul',
      address: 'Test Address, Kabul',
      area: 150,
      bedrooms: 3,
      bathrooms: 2,
      furnished: false,
      parking: true,
      garden: false,
      balcony: true
    };

    console.log('Property data:', JSON.stringify(propertyData, null, 2));

    const propertyResponse = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (propertyResponse.data.success) {
      console.log('✅ Property created successfully!');
      console.log('Property ID:', propertyResponse.data.data.id);
      console.log('Property Title:', propertyResponse.data.data.title);
    } else {
      console.log('❌ Property creation failed:', propertyResponse.data);
    }

  } catch (error) {
    console.error('❌ Error during property creation:');
    console.error('Status:', error.response?.status);
    console.error('Message:', error.response?.data?.message || error.message);
    console.error('Full error:', error.response?.data);
  }
}

// Run the test
testPropertyCreation();
