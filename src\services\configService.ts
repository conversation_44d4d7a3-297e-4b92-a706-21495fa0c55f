/**
 * Configuration Service
 * Provides dynamic configuration data for the application
 */

import { apiService } from './api';
import type {
  PropertyType,
  PropertyCategory,
  District,
  City,
  Province,
  PropertyFeature,
  Currency,
  SystemStats
} from '../types/config';

// Re-export types for backward compatibility
export type {
  PropertyType,
  PropertyCategory,
  District,
  City,
  Province,
  PropertyFeature,
  Currency,
  SystemStats
} from '../types/config';

// Configuration service class
export class ConfigService {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(key: string): string {
    return `config_${key}`;
  }

  private isValidCache(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < cached.ttl;
  }

  private setCache(key: string, data: any, ttl: number = this.CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private getCache(key: string): any {
    const cached = this.cache.get(key);
    return cached?.data;
  }

  // Get property types
  async getPropertyTypes(): Promise<PropertyType[]> {
    const cacheKey = this.getCacheKey('property_types');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/property-types');
      const data = response.data.data || [];
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch property types:', error);
      // Return fallback data
      return this.getFallbackPropertyTypes();
    }
  }

  // Get property categories
  async getPropertyCategories(): Promise<PropertyCategory[]> {
    const cacheKey = this.getCacheKey('property_categories');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/property-categories');
      const data = response.data.data || [];
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch property categories:', error);
      return this.getFallbackPropertyCategories();
    }
  }

  // Get provinces with cities and districts
  async getProvinces(): Promise<Province[]> {
    const cacheKey = this.getCacheKey('provinces');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/provinces');
      const data = response.data.data || [];
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch provinces:', error);
      return this.getFallbackProvinces();
    }
  }

  // Get cities by province
  async getCitiesByProvince(provinceId: string): Promise<City[]> {
    const provinces = await this.getProvinces();
    const province = provinces.find(p => p.id === provinceId);
    return province?.cities || [];
  }

  // Get districts by city
  async getDistrictsByCity(cityId: string): Promise<District[]> {
    const provinces = await this.getProvinces();
    for (const province of provinces) {
      const city = province.cities.find(c => c.id === cityId);
      if (city) {
        return city.districts || [];
      }
    }
    return [];
  }

  // Get property features
  async getPropertyFeatures(): Promise<PropertyFeature[]> {
    const cacheKey = this.getCacheKey('property_features');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/property-features');
      const data = response.data.data || [];
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch property features:', error);
      return this.getFallbackPropertyFeatures();
    }
  }

  // Get currencies
  async getCurrencies(): Promise<Currency[]> {
    const cacheKey = this.getCacheKey('currencies');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/currencies');
      const data = response.data.data || [];
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch currencies:', error);
      return this.getFallbackCurrencies();
    }
  }

  // Get system statistics
  async getSystemStats(): Promise<SystemStats> {
    const cacheKey = this.getCacheKey('system_stats');
    
    if (this.isValidCache(cacheKey)) {
      return this.getCache(cacheKey);
    }

    try {
      const response = await apiService.get('/config/system-stats');
      const data = response.data.data || {};
      this.setCache(cacheKey, data, 60000); // Cache for 1 minute
      return data;
    } catch (error) {
      console.error('Failed to fetch system stats:', error);
      return this.getFallbackSystemStats();
    }
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Clear specific cache
  clearCacheByKey(key: string): void {
    this.cache.delete(this.getCacheKey(key));
  }

  // Fallback data methods
  private getFallbackPropertyTypes(): PropertyType[] {
    return [
      { id: '1', name: 'HOUSE', label: 'House', isActive: true },
      { id: '2', name: 'APARTMENT', label: 'Apartment', isActive: true },
      { id: '3', name: 'VILLA', label: 'Villa', isActive: true },
      { id: '4', name: 'COMMERCIAL', label: 'Commercial', isActive: true },
      { id: '5', name: 'LAND', label: 'Land', isActive: true },
      { id: '6', name: 'OFFICE', label: 'Office', isActive: true }
    ];
  }

  private getFallbackPropertyCategories(): PropertyCategory[] {
    return [
      { id: '1', name: 'SALE', label: 'For Sale', isActive: true },
      { id: '2', name: 'RENT', label: 'For Rent', isActive: true },
      { id: '3', name: 'LEASE', label: 'For Lease', isActive: true }
    ];
  }

  private getFallbackProvinces(): Province[] {
    return [
      {
        id: '1',
        name: 'Kabul',
        code: 'KBL',
        isActive: true,
        cities: [
          {
            id: '1',
            name: 'Kabul',
            provinceId: '1',
            isActive: true,
            districts: [
              { id: '1', name: 'District 1', cityId: '1', isActive: true },
              { id: '2', name: 'District 2', cityId: '1', isActive: true },
              { id: '3', name: 'District 3', cityId: '1', isActive: true }
            ]
          }
        ]
      },
      {
        id: '2',
        name: 'Herat',
        code: 'HRT',
        isActive: true,
        cities: [
          {
            id: '2',
            name: 'Herat',
            provinceId: '2',
            isActive: true,
            districts: [
              { id: '4', name: 'District 1', cityId: '2', isActive: true },
              { id: '5', name: 'District 2', cityId: '2', isActive: true }
            ]
          }
        ]
      }
    ];
  }

  private getFallbackPropertyFeatures(): PropertyFeature[] {
    return [
      { id: '1', name: 'parking', label: 'Parking', category: 'feature', isActive: true },
      { id: '2', name: 'garden', label: 'Garden', category: 'feature', isActive: true },
      { id: '3', name: 'security', label: 'Security', category: 'feature', isActive: true },
      { id: '4', name: 'wifi', label: 'WiFi', category: 'amenity', isActive: true },
      { id: '5', name: 'heating', label: 'Heating', category: 'utility', isActive: true },
      { id: '6', name: 'ac', label: 'Air Conditioning', category: 'utility', isActive: true }
    ];
  }

  private getFallbackCurrencies(): Currency[] {
    return [
      { id: '1', code: 'AFN', name: 'Afghan Afghani', symbol: '؋', exchangeRate: 1, isDefault: true, isActive: true },
      { id: '2', code: 'USD', name: 'US Dollar', symbol: '$', exchangeRate: 0.014, isDefault: false, isActive: true },
      { id: '3', code: 'EUR', name: 'Euro', symbol: '€', exchangeRate: 0.012, isDefault: false, isActive: true }
    ];
  }

  private getFallbackSystemStats(): SystemStats {
    return {
      totalProperties: 0,
      totalUsers: 0,
      totalCities: 0,
      yearsExperience: 1,
      lastUpdated: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const configService = new ConfigService();
