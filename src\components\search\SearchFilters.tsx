import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon,
  MapPinIcon,
  HomeIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import Button from '../common/Button';
import Input from '../common/Input';
import { useLanguage } from '../../contexts/LanguageContext';
import { useConfig } from '../../hooks/useConfig';

interface FilterState {
  location: {
    province: string;
    city: string;
    district: string;
  };
  propertyType: string[];
  category: string[];
  priceRange: {
    min: number | '';
    max: number | '';
  };
  areaRange: {
    min: number | '';
    max: number | '';
  };
  bedrooms: string[];
  bathrooms: string[];
  features: string[];
  yearBuilt: {
    min: number | '';
    max: number | '';
  };
  furnished: string;
  parking: boolean;
  garden: boolean;
  balcony: boolean;
}

interface SearchFiltersProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters: (filters: FilterState) => void;
  onClearFilters: () => void;
  initialFilters?: Partial<FilterState>;
  resultCount?: number;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  isOpen,
  onClose,
  onApplyFilters,
  onClearFilters,
  initialFilters = {},
  resultCount
}) => {
  const [filters, setFilters] = useState<FilterState>({
    location: { province: '', city: '', district: '' },
    propertyType: [],
    category: [],
    priceRange: { min: '', max: '' },
    areaRange: { min: '', max: '' },
    bedrooms: [],
    bathrooms: [],
    features: [],
    yearBuilt: { min: '', max: '' },
    furnished: '',
    parking: false,
    garden: false,
    balcony: false,
    ...initialFilters
  });

  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['location', 'type', 'price'])
  );

  const { t, isRTL } = useLanguage();
  const {
    propertyTypes: configPropertyTypes,
    propertyCategories: configPropertyCategories,
    provinces: configProvinces,
    propertyFeatures: configPropertyFeatures,
    loading
  } = useConfig();

  // Convert config data to filter options
  const propertyTypes = configPropertyTypes.map(type => ({
    value: type.name.toLowerCase(),
    label: type.label
  }));

  const categories = configPropertyCategories.map(category => ({
    value: category.name.toLowerCase(),
    label: category.label
  }));

  const provinces = configProvinces.map(province => province.name);

  const features = configPropertyFeatures.map(feature => feature.label);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const updateFilter = <K extends keyof FilterState>(
    key: K,
    value: FilterState[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = <K extends keyof FilterState>(
    key: K,
    value: string
  ) => {
    setFilters(prev => {
      const currentArray = (prev[key] as string[]) || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray };
    });
  };

  const handleApplyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  const handleClearFilters = () => {
    setFilters({
      location: { province: '', city: '', district: '' },
      propertyType: [],
      category: [],
      priceRange: { min: '', max: '' },
      areaRange: { min: '', max: '' },
      bedrooms: [],
      bathrooms: [],
      features: [],
      yearBuilt: { min: '', max: '' },
      furnished: '',
      parking: false,
      garden: false,
      balcony: false
    });
    onClearFilters();
  };

  const renderSection = (
    key: string,
    title: string,
    icon: React.ReactNode,
    content: React.ReactNode
  ) => (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        onClick={() => toggleSection(key)}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div className="text-primary-600">{icon}</div>
          <span className="font-medium text-gray-900">{title}</span>
        </div>
        <ChevronDownIcon
          className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${
            expandedSections.has(key) ? 'rotate-180' : ''
          }`}
        />
      </button>

      <AnimatePresence>
        {expandedSections.has(key) && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="p-4 pt-0 space-y-4">
              {content}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onClose}
          />

          {/* Filter Panel */}
          <motion.div
            initial={{ x: isRTL ? '100%' : '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: isRTL ? '100%' : '-100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className={`fixed top-0 ${
              isRTL ? 'right-0' : 'left-0'
            } h-full w-full max-w-md bg-white shadow-2xl z-50 overflow-y-auto`}
          >
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FunnelIcon className="w-6 h-6 text-primary-600" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Search Filters
                </h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                icon={<XMarkIcon className="w-5 h-5" />}
              />
            </div>

            {/* Result Count */}
            {resultCount !== undefined && (
              <div className="p-4 bg-primary-50 border-b border-gray-200">
                <p className="text-sm text-primary-700">
                  <MagnifyingGlassIcon className="w-4 h-4 inline mr-1" />
                  {resultCount} properties found
                </p>
              </div>
            )}

            {/* Filter Content */}
            <div className="flex-1">
              {/* Location */}
              {renderSection(
                'location',
                'Location',
                <MapPinIcon className="w-5 h-5" />,
                <div className="space-y-4">
                  <select
                    value={filters.location.province}
                    onChange={(e) =>
                      updateFilter('location', {
                        ...filters.location,
                        province: e.target.value
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value="">Select Province</option>
                    {provinces.map(province => (
                      <option key={province} value={province}>
                        {province}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Property Type */}
              {renderSection(
                'type',
                'Property Type',
                <HomeIcon className="w-5 h-5" />,
                <div className="space-y-2">
                  {propertyTypes.map(type => (
                    <label key={type.value} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={filters.propertyType.includes(type.value)}
                        onChange={() => toggleArrayFilter('propertyType', type.value)}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-sm text-gray-700">{type.label}</span>
                    </label>
                  ))}
                </div>
              )}

              {/* Price Range */}
              {renderSection(
                'price',
                'Price Range',
                <CurrencyDollarIcon className="w-5 h-5" />,
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Min Price"
                    type="number"
                    value={filters.priceRange.min}
                    onChange={(e) =>
                      updateFilter('priceRange', {
                        ...filters.priceRange,
                        min: e.target.value ? parseInt(e.target.value) : ''
                      })
                    }
                    placeholder="0"
                  />
                  <Input
                    label="Max Price"
                    type="number"
                    value={filters.priceRange.max}
                    onChange={(e) =>
                      updateFilter('priceRange', {
                        ...filters.priceRange,
                        max: e.target.value ? parseInt(e.target.value) : ''
                      })
                    }
                    placeholder="Any"
                  />
                </div>
              )}

              {/* Bedrooms & Bathrooms */}
              {renderSection(
                'rooms',
                'Rooms',
                <HomeIcon className="w-5 h-5" />,
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bedrooms
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {['1', '2', '3', '4', '5+'].map(num => (
                        <button
                          key={num}
                          onClick={() => toggleArrayFilter('bedrooms', num)}
                          className={`px-3 py-1 rounded-lg border transition-colors ${
                            filters.bedrooms.includes(num)
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'bg-white text-gray-700 border-gray-300 hover:border-primary-300'
                          }`}
                        >
                          {num}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 space-y-3">
              <Button onClick={handleApplyFilters} fullWidth>
                Apply Filters
              </Button>
              <Button variant="outline" onClick={handleClearFilters} fullWidth>
                Clear All
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SearchFilters;
