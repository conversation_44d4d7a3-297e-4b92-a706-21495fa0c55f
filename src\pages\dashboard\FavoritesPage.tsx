import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { apiService } from '../../services/api';
import type { Property } from '../../services/api';
import PropertyCard from '../../components/common/PropertyCard';
import { 
  HeartIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ViewColumnsIcon,
  Squares2X2Icon,
  TrashIcon,
  ShareIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

// Remove the interface since we'll use the Property type from API

const FavoritesPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { t, isRTL } = useLanguage();
  const [favorites, setFavorites] = useState<Property[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'dateAdded' | 'price' | 'title'>('dateAdded');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedFavorites, setSelectedFavorites] = useState<number[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Mock data
  useEffect(() => {
    const mockFavorites: FavoriteProperty[] = [
      {
        id: 1,
        title: "Modern Villa in Wazir Akbar Khan",
        location: "Wazir Akbar Khan, Kabul",
        price: "$250,000",
        image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        beds: 4,
        baths: 3,
        area: "2,500 sq ft",
        type: "Villa",
        category: "For Sale",
        featured: true,
        views: 1247,
        saved: true,
        dateAdded: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
        priceChange: {
          oldPrice: "$270,000",
          newPrice: "$250,000",
          changeType: 'decrease',
          changeDate: new Date(Date.now() - 1000 * 60 * 60 * 12) // 12 hours ago
        }
      },
      {
        id: 2,
        title: "Luxury Apartment in Shahr-e-Naw",
        location: "Shahr-e-Naw, Kabul",
        price: "$180,000",
        image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        beds: 3,
        baths: 2,
        area: "1,800 sq ft",
        type: "Apartment",
        category: "For Sale",
        featured: false,
        views: 856,
        saved: true,
        dateAdded: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5) // 5 days ago
      },
      {
        id: 3,
        title: "Commercial Space in Chicken Street",
        location: "Chicken Street, Kabul",
        price: "$3,500/month",
        image: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
        beds: 0,
        baths: 2,
        area: "1,200 sq ft",
        type: "Commercial",
        category: "For Rent",
        featured: false,
        views: 432,
        saved: true,
        dateAdded: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) // 7 days ago
      }
    ];

    setFavorites(mockFavorites);
  }, []);

  const handleToggleFavorite = (propertyId: number) => {
    setFavorites(prev => prev.filter(fav => fav.id !== propertyId));
  };

  const handleSelectFavorite = (propertyId: number) => {
    setSelectedFavorites(prev => 
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    );
  };

  const handleSelectAll = () => {
    setSelectedFavorites(
      selectedFavorites.length === favorites.length 
        ? [] 
        : favorites.map(fav => fav.id)
    );
  };

  const handleRemoveSelected = () => {
    setFavorites(prev => prev.filter(fav => !selectedFavorites.includes(fav.id)));
    setSelectedFavorites([]);
  };

  const filteredAndSortedFavorites = favorites
    .filter(fav => 
      fav.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fav.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      fav.type.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'dateAdded':
          return b.dateAdded.getTime() - a.dateAdded.getTime();
        case 'price':
          const priceA = parseInt(a.price.replace(/[^0-9]/g, ''));
          const priceB = parseInt(b.price.replace(/[^0-9]/g, ''));
          return priceB - priceA;
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

  const formatDateAdded = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    return `${days} days ago`;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Please log in to view favorites</h2>
          <Link to="/login" className="btn-primary">
            Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/dashboard" className="text-gray-500 hover:text-primary-500 transition-colors duration-300">
                ← Dashboard
              </Link>
              <div className="flex items-center space-x-2">
                <HeartSolidIcon className="w-6 h-6 text-red-500" />
                <h1 className="text-xl font-bold text-gray-900">My Favorites</h1>
                <span className="bg-primary-100 text-primary-800 text-sm font-medium px-2 py-1 rounded-full">
                  {favorites.length}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {favorites.length === 0 ? (
          /* Empty State */
          <div className="text-center py-16">
            <HeartIcon className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">No favorites yet</h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Start exploring properties and save your favorites by clicking the heart icon on any property you like.
            </p>
            <Link to="/listings" className="btn-primary">
              Browse Properties
            </Link>
          </div>
        ) : (
          <>
            {/* Controls */}
            <div className="bg-white rounded-xl shadow-soft p-6 mb-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                {/* Search */}
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search favorites..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                </div>

                {/* Controls */}
                <div className="flex items-center space-x-4">
                  {/* Sort */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  >
                    <option value="dateAdded">Recently Added</option>
                    <option value="price">Price</option>
                    <option value="title">Name</option>
                  </select>

                  {/* View Mode */}
                  <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 transition-colors duration-300 ${
                        viewMode === 'grid' 
                          ? 'bg-primary-500 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      <Squares2X2Icon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 transition-colors duration-300 ${
                        viewMode === 'list' 
                          ? 'bg-primary-500 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      <ViewColumnsIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Bulk Actions */}
              {selectedFavorites.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      {selectedFavorites.length} selected
                    </span>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={handleSelectAll}
                        className="text-sm text-primary-500 hover:text-primary-600 transition-colors duration-300"
                      >
                        {selectedFavorites.length === favorites.length ? 'Deselect All' : 'Select All'}
                      </button>
                      <button
                        onClick={handleRemoveSelected}
                        className="flex items-center space-x-1 text-sm text-red-500 hover:text-red-600 transition-colors duration-300"
                      >
                        <TrashIcon className="w-4 h-4" />
                        <span>Remove</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Properties Grid/List */}
            <div className={`${
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
                : 'space-y-6'
            }`}>
              {filteredAndSortedFavorites.map((property, index) => (
                <div key={property.id} className="relative group">
                  {/* Selection Checkbox */}
                  <div className="absolute top-4 left-4 z-10">
                    <input
                      type="checkbox"
                      checked={selectedFavorites.includes(property.id)}
                      onChange={() => handleSelectFavorite(property.id)}
                      className="w-5 h-5 text-primary-500 bg-white border-gray-300 rounded focus:ring-primary-200 focus:ring-2"
                    />
                  </div>

                  {/* Price Change Badge */}
                  {property.priceChange && (
                    <div className="absolute top-4 right-4 z-10">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        property.priceChange.changeType === 'decrease'
                          ? 'bg-secondary-100 text-secondary-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {property.priceChange.changeType === 'decrease' ? '↓' : '↑'} Price Changed
                      </span>
                    </div>
                  )}

                  <PropertyCard
                    property={property}
                    viewMode={viewMode}
                    onToggleSaved={handleToggleFavorite}
                    animationDelay={index * 100}
                  />

                  {/* Additional Info */}
                  <div className="mt-2 text-sm text-gray-500">
                    Added {formatDateAdded(property.dateAdded)}
                  </div>
                </div>
              ))}
            </div>

            {/* No Results */}
            {filteredAndSortedFavorites.length === 0 && searchQuery && (
              <div className="text-center py-16">
                <MagnifyingGlassIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No favorites found
                </h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or browse more properties.
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;
