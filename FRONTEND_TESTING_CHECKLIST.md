# 🧪 Frontend Testing Checklist

## 📋 Manual Testing Guide

### 🌐 Access the Application
- **URL**: http://localhost:5174
- **Backend**: http://localhost:3001 (should be running)

---

## 🔐 Authentication Testing

### ✅ User Registration
1. **Navigate to Registration**
   - Click "Sign Up" or "Register" button
   - Should redirect to registration form

2. **Test Registration Form**
   - Fill in required fields:
     - Name: "Test User"
     - Email: "<EMAIL>"
     - Password: "TestPassword123!"
     - Phone: "+93701234567"
   - Submit form
   - Should show success message
   - Should redirect to dashboard or login

3. **Test Validation**
   - Try submitting empty form (should show validation errors)
   - Try invalid email format (should show error)
   - Try weak password (should show error)
   - Try invalid phone number (should show error)

### ✅ User Login
1. **Navigate to Login**
   - Click "Sign In" or "Login" button
   - Should show login form

2. **Test Login Form**
   - Enter registered email and password
   - Submit form
   - Should show success message
   - Should redirect to dashboard
   - Should show user name in header/navigation

3. **Test Invalid Login**
   - Try wrong email/password combination
   - Should show error message
   - Should not redirect

### ✅ User Logout
1. **Test Logout**
   - Click logout button (usually in user menu)
   - Should redirect to home page
   - Should clear user session
   - Should show login/register options again

---

## 🏠 Property Management Testing

### ✅ View Properties
1. **Browse Properties**
   - Navigate to properties page
   - Should show list of properties
   - Each property should display:
     - Title, price, location
     - Property type and category
     - Images (if available)
     - Basic details

2. **Property Details**
   - Click on a property
   - Should show detailed view with:
     - Full description
     - All images
     - Property features
     - Contact information
     - Map/location (if implemented)

### ✅ Create Property (Authenticated Users)
1. **Access Property Creation**
   - Login as user
   - Navigate to "Add Property" or "Create Listing"
   - Should show multi-step form

2. **Test Property Form**
   - **Step 1: Basic Info**
     - Title: "Beautiful Test House"
     - Description: "A lovely property for testing"
     - Category: Sale/Rent
     - Type: House/Apartment/etc.
   
   - **Step 2: Location**
     - Province: Kabul
     - City: Kabul
     - District: District 1
     - Address: "123 Test Street"
   
   - **Step 3: Details**
     - Price: 150000
     - Area: 1500 sqm
     - Bedrooms: 3
     - Bathrooms: 2
   
   - **Step 4: Features**
     - Select amenities (parking, garden, etc.)
     - Select features (wifi, heating, etc.)
   
   - **Step 5: Images**
     - Upload property images
     - Test drag & drop functionality

3. **Submit Property**
   - Complete all steps
   - Submit form
   - Should show success message
   - Property should appear in listings

### ✅ Search and Filter
1. **Text Search**
   - Use search bar
   - Enter property title or description keywords
   - Should filter results accordingly

2. **Location Filter**
   - Select province/city/district
   - Should filter properties by location

3. **Price Filter**
   - Set min/max price range
   - Should filter properties within range

4. **Property Type Filter**
   - Select house/apartment/villa/etc.
   - Should filter by property type

5. **Features Filter**
   - Select specific amenities
   - Should filter properties with those features

---

## 👤 User Dashboard Testing

### ✅ Profile Management
1. **View Profile**
   - Navigate to user profile/dashboard
   - Should show user information
   - Should display user's properties

2. **Edit Profile**
   - Click edit profile
   - Update name, phone, bio, etc.
   - Save changes
   - Should show success message
   - Changes should be reflected

3. **Change Password**
   - Navigate to password change
   - Enter current and new password
   - Submit form
   - Should show success message

### ✅ My Properties
1. **View My Properties**
   - Should list user's created properties
   - Show property status (pending, approved, etc.)

2. **Edit Property**
   - Click edit on a property
   - Should open property form with existing data
   - Make changes and save
   - Should update successfully

3. **Delete Property**
   - Click delete on a property
   - Should show confirmation dialog
   - Confirm deletion
   - Property should be removed

### ✅ Favorites System
1. **Add to Favorites**
   - View a property (not owned by current user)
   - Click "Add to Favorites" or heart icon
   - Should show success message
   - Icon should change to indicate favorited

2. **View Favorites**
   - Navigate to favorites page
   - Should list all favorited properties

3. **Remove from Favorites**
   - Click remove/unfavorite button
   - Should remove from favorites list

---

## 💬 Messaging System Testing

### ✅ Property Inquiries
1. **Send Message about Property**
   - View a property
   - Click "Contact Owner" or "Send Message"
   - Fill in message form
   - Send message
   - Should show success confirmation

2. **View Messages**
   - Navigate to messages/inbox
   - Should show conversation threads
   - Click on a conversation
   - Should show message history

3. **Reply to Messages**
   - Open a conversation
   - Type and send reply
   - Should appear in conversation
   - Should update in real-time (if implemented)

---

## 🌐 Multi-language Testing

### ✅ Language Switching
1. **Test Language Options**
   - Look for language selector
   - Should offer: English, Pashto, Dari

2. **Switch to Pashto**
   - Select Pashto from language menu
   - Interface should switch to Pashto
   - Text should be right-to-left (RTL)
   - All UI elements should be translated

3. **Switch to Dari**
   - Select Dari from language menu
   - Interface should switch to Dari
   - Text should be right-to-left (RTL)
   - All UI elements should be translated

4. **Switch back to English**
   - Select English
   - Should return to left-to-right layout
   - All text should be in English

---

## 📱 Responsive Design Testing

### ✅ Mobile View
1. **Test on Mobile Screen**
   - Resize browser to mobile width (375px)
   - Or use browser dev tools mobile view
   - Navigation should collapse to hamburger menu
   - All content should be readable
   - Forms should be usable

2. **Test Tablet View**
   - Resize to tablet width (768px)
   - Layout should adapt appropriately
   - All functionality should work

---

## 🔧 Error Handling Testing

### ✅ Network Errors
1. **Test Offline Behavior**
   - Disconnect internet
   - Try to perform actions
   - Should show appropriate error messages

2. **Test Server Errors**
   - Stop backend server
   - Try to perform actions
   - Should show connection error messages

### ✅ Form Validation
1. **Test Required Fields**
   - Submit forms with empty required fields
   - Should show validation errors

2. **Test Field Formats**
   - Enter invalid email formats
   - Enter invalid phone numbers
   - Should show format validation errors

---

## ✅ Performance Testing

### ✅ Page Load Times
1. **Test Initial Load**
   - Refresh page and measure load time
   - Should load within 3-5 seconds

2. **Test Navigation**
   - Navigate between pages
   - Should be responsive and fast

### ✅ Image Loading
1. **Test Property Images**
   - View properties with images
   - Images should load properly
   - Should have loading states

---

## 🎯 Test Results Summary

### ✅ Completed Tests
- [ ] User Registration
- [ ] User Login
- [ ] User Logout
- [ ] View Properties
- [ ] Property Details
- [ ] Create Property
- [ ] Search Properties
- [ ] Filter Properties
- [ ] User Profile
- [ ] Edit Profile
- [ ] My Properties
- [ ] Favorites System
- [ ] Messaging System
- [ ] Language Switching
- [ ] Mobile Responsive
- [ ] Error Handling

### 🐛 Issues Found
(Document any issues discovered during testing)

### 📝 Notes
(Add any additional observations or recommendations)

---

## 🚀 Next Steps
After completing manual testing:
1. Document any bugs found
2. Test admin panel functionality
3. Perform end-to-end user journey testing
4. Test with multiple users simultaneously
5. Verify data persistence across sessions
