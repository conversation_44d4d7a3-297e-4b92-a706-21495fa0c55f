{"name": "realestate-backend", "version": "1.0.0", "description": "Real Estate Platform Backend API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "node comprehensive-test.js"}, "keywords": ["real-estate", "api", "nodejs", "express", "prisma"], "author": "Real Estate Team", "license": "MIT", "dependencies": {"@hookform/resolvers": "^5.2.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.5", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "validator": "^13.15.15", "zod": "^4.0.10"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.1.0", "@types/nodemailer": "^6.4.17", "@types/validator": "^13.15.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}