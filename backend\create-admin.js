const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// User schema (simplified for this script)
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String, required: true },
  role: { type: String, enum: ['USER', 'AGENT', 'ADMIN', 'SUPER_ADMIN'], default: 'USER' },
  isActive: { type: Boolean, default: true },
  isVerified: { type: Boolean, default: false }
}, { timestamps: true });

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

const User = mongoose.model('User', userSchema);

async function createAdmin() {
  try {
    // Connect to MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/realestate_db';
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('ℹ️  Admin user already exists');
      console.log('Admin details:', {
        email: existingAdmin.email,
        name: existingAdmin.name,
        role: existingAdmin.role,
        isActive: existingAdmin.isActive
      });
    } else {
      // Create admin user
      const admin = new User({
        email: '<EMAIL>',
        password: 'admin123456',
        name: 'System Administrator',
        role: 'SUPER_ADMIN',
        isActive: true,
        isVerified: true
      });

      await admin.save();
      console.log('✅ Admin user created successfully');
      console.log('Admin details:', {
        email: admin.email,
        name: admin.name,
        role: admin.role,
        isActive: admin.isActive
      });
    }

    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

createAdmin();
