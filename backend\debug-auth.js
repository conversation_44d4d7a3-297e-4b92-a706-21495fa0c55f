const axios = require("axios");

const BASE_URL = "http://localhost:3001/api";

const testUser = {
  name: "Test User",
  email: `test${Date.now()}@example.com`,
  password: "TestPassword123!",
  phone: "+93701234567",
};

async function debugAuth() {
  console.log("🔍 Debugging Authentication...");
  console.log("Test user:", testUser);

  try {
    // Test registration
    console.log("\n📝 Testing Registration...");
    const registerResponse = await axios.post(
      `${BASE_URL}/auth/register`,
      testUser
    );
    console.log("✅ Registration Success:", registerResponse.status);
    console.log(
      "Response data:",
      JSON.stringify(registerResponse.data, null, 2)
    );

    // Test login
    console.log("\n🔐 Testing Login...");
    const loginData = {
      email: testUser.email,
      password: testUser.password,
    };
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, loginData);
    console.log("✅ Login Success:", loginResponse.status);
    console.log("Response data:", JSON.stringify(loginResponse.data, null, 2));
  } catch (error) {
    console.error("❌ Error:", error.response?.status);
    console.error("Error data:", JSON.stringify(error.response?.data, null, 2));
    console.error("Error message:", error.message);
  }
}

debugAuth();
