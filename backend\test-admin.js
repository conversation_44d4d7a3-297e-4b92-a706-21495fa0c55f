const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testAdminFunctionality() {
  console.log('👑 Testing Admin Functionality...\n');
  
  try {
    // Test Admin Login
    console.log('1. Testing Admin Login...');
    const adminLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123456'
    });
    console.log('✅ Admin Login:', adminLoginResponse.status, 'Login successful');
    
    const adminToken = adminLoginResponse.data.data.tokens.accessToken;
    const adminUser = adminLoginResponse.data.data.user;
    console.log('   Admin Role:', adminUser.role);
    console.log('   Admin Name:', adminUser.name);
    
    // Test Admin Profile
    console.log('\n2. Testing Admin Profile...');
    const adminProfileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Admin Profile:', adminProfileResponse.status, `Role: ${adminProfileResponse.data.data.user.role}`);
    
    // Test Get All Users (Admin only)
    console.log('\n3. Testing Get All Users (Admin Only)...');
    const usersResponse = await axios.get(`${BASE_URL}/users`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Get All Users:', usersResponse.status, `Found ${usersResponse.data.data?.length || 0} users`);
    
    // Test Dashboard Stats
    console.log('\n4. Testing Dashboard Stats...');
    const statsResponse = await axios.get(`${BASE_URL}/users/dashboard/stats`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Dashboard Stats:', statsResponse.status, 'Stats retrieved');
    if (statsResponse.data.data) {
      console.log('   Total Users:', statsResponse.data.data.totalUsers || 0);
      console.log('   Total Properties:', statsResponse.data.data.totalProperties || 0);
      console.log('   Total Messages:', statsResponse.data.data.totalMessages || 0);
    }
    
    console.log('\n🎉 Admin functionality tests completed!');
    
  } catch (error) {
    console.error('❌ Admin test failed:', error.response?.status, error.response?.data || error.message);
    
    if (error.response?.status === 403) {
      console.log('ℹ️  This might be expected if the user doesn\'t have admin privileges');
    }
  }
}

testAdminFunctionality();
