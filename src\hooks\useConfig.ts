/**
 * Configuration Hook
 * Provides access to dynamic configuration data
 */

import { useState, useEffect, useCallback } from 'react';
import { configService } from '../services/configService';
import type {
  PropertyType,
  PropertyCategory,
  Province,
  City,
  District,
  PropertyFeature,
  Currency,
  SystemStats
} from '../types/config';

interface UseConfigResult {
  // Data
  propertyTypes: PropertyType[];
  propertyCategories: PropertyCategory[];
  provinces: Province[];
  propertyFeatures: PropertyFeature[];
  currencies: Currency[];
  systemStats: SystemStats | null;
  
  // Loading states
  loading: {
    propertyTypes: boolean;
    propertyCategories: boolean;
    provinces: boolean;
    propertyFeatures: boolean;
    currencies: boolean;
    systemStats: boolean;
  };
  
  // Error states
  errors: {
    propertyTypes: string | null;
    propertyCategories: string | null;
    provinces: string | null;
    propertyFeatures: string | null;
    currencies: string | null;
    systemStats: string | null;
  };
  
  // Helper functions
  getCitiesByProvince: (provinceId: string) => City[];
  getDistrictsByCity: (cityId: string) => District[];
  getPropertyTypeById: (id: string) => PropertyType | undefined;
  getPropertyCategoryById: (id: string) => PropertyCategory | undefined;
  getProvinceById: (id: string) => Province | undefined;
  getCityById: (cityId: string) => City | undefined;
  getDistrictById: (districtId: string) => District | undefined;
  getFeatureById: (id: string) => PropertyFeature | undefined;
  getCurrencyById: (id: string) => Currency | undefined;
  getDefaultCurrency: () => Currency | undefined;
  
  // Actions
  refresh: () => Promise<void>;
  refreshPropertyTypes: () => Promise<void>;
  refreshPropertyCategories: () => Promise<void>;
  refreshProvinces: () => Promise<void>;
  refreshPropertyFeatures: () => Promise<void>;
  refreshCurrencies: () => Promise<void>;
  refreshSystemStats: () => Promise<void>;
}

export const useConfig = (): UseConfigResult => {
  // State
  const [propertyTypes, setPropertyTypes] = useState<PropertyType[]>([]);
  const [propertyCategories, setPropertyCategories] = useState<PropertyCategory[]>([]);
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [propertyFeatures, setPropertyFeatures] = useState<PropertyFeature[]>([]);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  
  // Loading states
  const [loading, setLoading] = useState({
    propertyTypes: false,
    propertyCategories: false,
    provinces: false,
    propertyFeatures: false,
    currencies: false,
    systemStats: false,
  });
  
  // Error states
  const [errors, setErrors] = useState({
    propertyTypes: null as string | null,
    propertyCategories: null as string | null,
    provinces: null as string | null,
    propertyFeatures: null as string | null,
    currencies: null as string | null,
    systemStats: null as string | null,
  });

  // Helper function to update loading state
  const setLoadingState = useCallback((key: keyof typeof loading, value: boolean) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  }, []);

  // Helper function to update error state
  const setErrorState = useCallback((key: keyof typeof errors, value: string | null) => {
    setErrors(prev => ({ ...prev, [key]: value }));
  }, []);

  // Load property types
  const loadPropertyTypes = useCallback(async () => {
    setLoadingState('propertyTypes', true);
    setErrorState('propertyTypes', null);
    
    try {
      const data = await configService.getPropertyTypes();
      setPropertyTypes(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load property types';
      setErrorState('propertyTypes', message);
    } finally {
      setLoadingState('propertyTypes', false);
    }
  }, [setLoadingState, setErrorState]);

  // Load property categories
  const loadPropertyCategories = useCallback(async () => {
    setLoadingState('propertyCategories', true);
    setErrorState('propertyCategories', null);
    
    try {
      const data = await configService.getPropertyCategories();
      setPropertyCategories(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load property categories';
      setErrorState('propertyCategories', message);
    } finally {
      setLoadingState('propertyCategories', false);
    }
  }, [setLoadingState, setErrorState]);

  // Load provinces
  const loadProvinces = useCallback(async () => {
    setLoadingState('provinces', true);
    setErrorState('provinces', null);
    
    try {
      const data = await configService.getProvinces();
      setProvinces(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load provinces';
      setErrorState('provinces', message);
    } finally {
      setLoadingState('provinces', false);
    }
  }, [setLoadingState, setErrorState]);

  // Load property features
  const loadPropertyFeatures = useCallback(async () => {
    setLoadingState('propertyFeatures', true);
    setErrorState('propertyFeatures', null);
    
    try {
      const data = await configService.getPropertyFeatures();
      setPropertyFeatures(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load property features';
      setErrorState('propertyFeatures', message);
    } finally {
      setLoadingState('propertyFeatures', false);
    }
  }, [setLoadingState, setErrorState]);

  // Load currencies
  const loadCurrencies = useCallback(async () => {
    setLoadingState('currencies', true);
    setErrorState('currencies', null);
    
    try {
      const data = await configService.getCurrencies();
      setCurrencies(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load currencies';
      setErrorState('currencies', message);
    } finally {
      setLoadingState('currencies', false);
    }
  }, [setLoadingState, setErrorState]);

  // Load system stats
  const loadSystemStats = useCallback(async () => {
    setLoadingState('systemStats', true);
    setErrorState('systemStats', null);
    
    try {
      const data = await configService.getSystemStats();
      setSystemStats(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load system stats';
      setErrorState('systemStats', message);
    } finally {
      setLoadingState('systemStats', false);
    }
  }, [setLoadingState, setErrorState]);

  // Helper functions
  const getCitiesByProvince = useCallback((provinceId: string): City[] => {
    const province = provinces.find(p => p.id === provinceId);
    return province?.cities || [];
  }, [provinces]);

  const getDistrictsByCity = useCallback((cityId: string): District[] => {
    for (const province of provinces) {
      const city = province.cities.find(c => c.id === cityId);
      if (city) {
        return city.districts || [];
      }
    }
    return [];
  }, [provinces]);

  const getPropertyTypeById = useCallback((id: string): PropertyType | undefined => {
    return propertyTypes.find(type => type.id === id);
  }, [propertyTypes]);

  const getPropertyCategoryById = useCallback((id: string): PropertyCategory | undefined => {
    return propertyCategories.find(category => category.id === id);
  }, [propertyCategories]);

  const getProvinceById = useCallback((id: string): Province | undefined => {
    return provinces.find(province => province.id === id);
  }, [provinces]);

  const getCityById = useCallback((cityId: string): City | undefined => {
    for (const province of provinces) {
      const city = province.cities.find(c => c.id === cityId);
      if (city) return city;
    }
    return undefined;
  }, [provinces]);

  const getDistrictById = useCallback((districtId: string): District | undefined => {
    for (const province of provinces) {
      for (const city of province.cities) {
        const district = city.districts.find(d => d.id === districtId);
        if (district) return district;
      }
    }
    return undefined;
  }, [provinces]);

  const getFeatureById = useCallback((id: string): PropertyFeature | undefined => {
    return propertyFeatures.find(feature => feature.id === id);
  }, [propertyFeatures]);

  const getCurrencyById = useCallback((id: string): Currency | undefined => {
    return currencies.find(currency => currency.id === id);
  }, [currencies]);

  const getDefaultCurrency = useCallback((): Currency | undefined => {
    return currencies.find(currency => currency.isDefault);
  }, [currencies]);

  // Refresh functions
  const refreshPropertyTypes = useCallback(async () => {
    configService.clearCacheByKey('property_types');
    await loadPropertyTypes();
  }, [loadPropertyTypes]);

  const refreshPropertyCategories = useCallback(async () => {
    configService.clearCacheByKey('property_categories');
    await loadPropertyCategories();
  }, [loadPropertyCategories]);

  const refreshProvinces = useCallback(async () => {
    configService.clearCacheByKey('provinces');
    await loadProvinces();
  }, [loadProvinces]);

  const refreshPropertyFeatures = useCallback(async () => {
    configService.clearCacheByKey('property_features');
    await loadPropertyFeatures();
  }, [loadPropertyFeatures]);

  const refreshCurrencies = useCallback(async () => {
    configService.clearCacheByKey('currencies');
    await loadCurrencies();
  }, [loadCurrencies]);

  const refreshSystemStats = useCallback(async () => {
    configService.clearCacheByKey('system_stats');
    await loadSystemStats();
  }, [loadSystemStats]);

  const refresh = useCallback(async () => {
    configService.clearCache();
    await Promise.all([
      loadPropertyTypes(),
      loadPropertyCategories(),
      loadProvinces(),
      loadPropertyFeatures(),
      loadCurrencies(),
      loadSystemStats(),
    ]);
  }, [
    loadPropertyTypes,
    loadPropertyCategories,
    loadProvinces,
    loadPropertyFeatures,
    loadCurrencies,
    loadSystemStats,
  ]);

  // Load data on mount
  useEffect(() => {
    loadPropertyTypes();
    loadPropertyCategories();
    loadProvinces();
    loadPropertyFeatures();
    loadCurrencies();
    loadSystemStats();
  }, [
    loadPropertyTypes,
    loadPropertyCategories,
    loadProvinces,
    loadPropertyFeatures,
    loadCurrencies,
    loadSystemStats,
  ]);

  return {
    // Data
    propertyTypes,
    propertyCategories,
    provinces,
    propertyFeatures,
    currencies,
    systemStats,
    
    // Loading states
    loading,
    
    // Error states
    errors,
    
    // Helper functions
    getCitiesByProvince,
    getDistrictsByCity,
    getPropertyTypeById,
    getPropertyCategoryById,
    getProvinceById,
    getCityById,
    getDistrictById,
    getFeatureById,
    getCurrencyById,
    getDefaultCurrency,
    
    // Actions
    refresh,
    refreshPropertyTypes,
    refreshPropertyCategories,
    refreshProvinces,
    refreshPropertyFeatures,
    refreshCurrencies,
    refreshSystemStats,
  };
};
