const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testUserDashboard() {
  console.log('👤 Testing User Dashboard and Profile...\n');
  
  try {
    // Create a test user
    console.log('1. Creating test user...');
    const userData = {
      name: 'Dashboard Test User',
      email: `dashboard_${Date.now()}@example.com`,
      password: 'TestPassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const userResponse = await axios.post(`${BASE_URL}/auth/register`, userData);
    const userToken = userResponse.data.data.tokens.accessToken;
    const userId = userResponse.data.data.user.id;
    console.log('✅ User created:', userData.name);
    
    await delay(1000);
    
    // Test get user profile
    console.log('\n2. Testing get user profile...');
    const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Get profile:', profileResponse.status);
    console.log('   User name:', profileResponse.data.data.user.name);
    console.log('   User email:', profileResponse.data.data.user.email);
    console.log('   User role:', profileResponse.data.data.user.role);
    
    await delay(1000);
    
    // Test update user profile
    console.log('\n3. Testing update user profile...');
    const updateData = {
      name: 'Updated Dashboard User',
      bio: 'This is my updated bio for testing',
      location: 'Kabul, Afghanistan'
    };
    
    const updateResponse = await axios.put(`${BASE_URL}/auth/profile`, updateData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Update profile:', updateResponse.status);
    console.log('   Updated name:', updateResponse.data.data.name);
    console.log('   Updated bio:', updateResponse.data.data.bio);
    
    await delay(1000);
    
    // Create some test properties for dashboard stats
    console.log('\n4. Creating test properties for dashboard...');
    const properties = [
      {
        title: 'Dashboard Test House 1',
        description: 'First test property for dashboard',
        price: 180000,
        category: 'SALE',
        type: 'HOUSE',
        province: 'Kabul',
        city: 'Kabul',
        district: 'District 1',
        address: '123 Dashboard Street',
        area: 1800,
        bedrooms: 3,
        bathrooms: 2
      },
      {
        title: 'Dashboard Test Apartment 1',
        description: 'First test apartment for dashboard',
        price: 120000,
        category: 'RENT',
        type: 'APARTMENT',
        province: 'Kabul',
        city: 'Kabul',
        district: 'District 2',
        address: '456 Dashboard Avenue',
        area: 1200,
        bedrooms: 2,
        bathrooms: 1
      }
    ];
    
    const createdProperties = [];
    for (let i = 0; i < properties.length; i++) {
      const propertyResponse = await axios.post(`${BASE_URL}/properties`, properties[i], {
        headers: { Authorization: `Bearer ${userToken}` }
      });
      createdProperties.push(propertyResponse.data.data);
      console.log(`   ✅ Property ${i + 1} created:`, properties[i].title);
      await delay(500);
    }
    
    await delay(1000);
    
    // Test get user's properties
    console.log('\n5. Testing get user properties...');
    const userPropertiesResponse = await axios.get(`${BASE_URL}/properties/user/my-properties`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Get user properties:', userPropertiesResponse.status);
    console.log(`   Found ${userPropertiesResponse.data.data?.length || 0} properties`);
    
    await delay(1000);
    
    // Test dashboard statistics
    console.log('\n6. Testing dashboard statistics...');
    const statsResponse = await axios.get(`${BASE_URL}/users/dashboard/stats`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Dashboard stats:', statsResponse.status);
    if (statsResponse.data.data) {
      console.log('   Total Properties:', statsResponse.data.data.totalProperties);
      console.log('   Active Properties:', statsResponse.data.data.activeProperties);
      console.log('   Total Favorites:', statsResponse.data.data.totalFavorites);
      console.log('   Unread Messages:', statsResponse.data.data.unreadMessages);
      console.log('   Recent Properties:', statsResponse.data.data.recentProperties?.length || 0);
    }
    
    await delay(1000);
    
    // Test favorites functionality
    console.log('\n7. Testing favorites functionality...');
    
    // Create another user to create a property to favorite
    const otherUserData = {
      name: 'Other User',
      email: `other_${Date.now()}@example.com`,
      password: 'TestPassword123!',
      phone: `+9370${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`
    };
    
    const otherUserResponse = await axios.post(`${BASE_URL}/auth/register`, otherUserData);
    const otherUserToken = otherUserResponse.data.data.tokens.accessToken;
    
    await delay(500);
    
    // Create a property by the other user
    const otherPropertyData = {
      title: 'Property to Favorite',
      description: 'A property created by another user to test favorites',
      price: 200000,
      category: 'SALE',
      type: 'VILLA',
      province: 'Kabul',
      city: 'Kabul',
      district: 'District 3',
      address: '789 Favorite Street',
      area: 2500,
      bedrooms: 4,
      bathrooms: 3
    };
    
    const otherPropertyResponse = await axios.post(`${BASE_URL}/properties`, otherPropertyData, {
      headers: { Authorization: `Bearer ${otherUserToken}` }
    });
    const otherPropertyId = otherPropertyResponse.data.data._id || otherPropertyResponse.data.data.id;
    console.log('   ✅ Other user property created for favorites test');
    
    await delay(500);
    
    // Add to favorites
    const addFavoriteResponse = await axios.post(`${BASE_URL}/users/favorites/${otherPropertyId}`, {}, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('   ✅ Add to favorites:', addFavoriteResponse.status);
    
    await delay(500);
    
    // Get favorites
    const favoritesResponse = await axios.get(`${BASE_URL}/users/favorites`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('   ✅ Get favorites:', favoritesResponse.status);
    console.log(`   Found ${favoritesResponse.data.data?.length || 0} favorites`);
    
    await delay(500);
    
    // Remove from favorites
    const removeFavoriteResponse = await axios.delete(`${BASE_URL}/users/favorites/${otherPropertyId}`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('   ✅ Remove from favorites:', removeFavoriteResponse.status);
    
    await delay(1000);
    
    // Test password change
    console.log('\n8. Testing password change...');
    const passwordChangeData = {
      currentPassword: userData.password,
      newPassword: 'NewTestPassword123!',
      confirmPassword: 'NewTestPassword123!'
    };
    
    const passwordChangeResponse = await axios.put(`${BASE_URL}/auth/change-password`, passwordChangeData, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('✅ Password change:', passwordChangeResponse.status);
    
    await delay(1000);
    
    // Test login with new password
    console.log('\n9. Testing login with new password...');
    const newLoginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: userData.email,
      password: 'NewTestPassword123!'
    });
    console.log('✅ Login with new password:', newLoginResponse.status);
    
    console.log('\n🎉 User dashboard and profile tests completed successfully!');
    
  } catch (error) {
    console.error('❌ User dashboard test failed:', error.response?.status, error.response?.data || error.message);
  }
}

testUserDashboard();
