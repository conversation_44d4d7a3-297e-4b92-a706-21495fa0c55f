import { Router, Request, Response } from 'express';
import { Property, User } from '../models';

const router = Router();

// Test route
router.get('/test', (req: Request, res: Response) => {
  res.json({ success: true, message: 'Config routes are working!' });
});

// Property Types Configuration
router.get('/property-types', async (req: Request, res: Response) => {
  try {
    const propertyTypes = [
      { id: '1', name: 'HOUSE', label: 'House', isActive: true },
      { id: '2', name: 'APARTMENT', label: 'Apartment', isActive: true },
      { id: '3', name: 'VILLA', label: 'Villa', isActive: true },
      { id: '4', name: 'COMMERCIAL', label: 'Commercial', isActive: true },
      { id: '5', name: 'LAND', label: 'Land', isActive: true },
      { id: '6', name: 'OFFICE', label: 'Office', isActive: true },
      { id: '7', name: 'WAREHOUSE', label: 'Warehouse', isActive: true },
      { id: '8', name: 'SHOP', label: 'Shop', isActive: true }
    ];

    res.json({
      success: true,
      data: propertyTypes
    });
  } catch (error) {
    console.error('Error fetching property types:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch property types',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

// Property Categories Configuration
router.get('/property-categories', async (req: Request, res: Response) => {
  try {
    const propertyCategories = [
      { id: '1', name: 'SALE', label: 'For Sale', isActive: true },
      { id: '2', name: 'RENT', label: 'For Rent', isActive: true },
      { id: '3', name: 'LEASE', label: 'For Lease', isActive: true }
    ];

    res.json({
      success: true,
      data: propertyCategories
    });
  } catch (error) {
    console.error('Error fetching property categories:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch property categories',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

// Provinces with Cities and Districts Configuration
router.get('/provinces', async (req: Request, res: Response) => {
  try {
    const provinces = [
      {
        id: '1',
        name: 'Kabul',
        code: 'KBL',
        isActive: true,
        cities: [
          {
            id: '1',
            name: 'Kabul',
            provinceId: '1',
            isActive: true,
            districts: [
              { id: '1', name: 'District 1', cityId: '1', isActive: true },
              { id: '2', name: 'District 2', cityId: '1', isActive: true },
              { id: '3', name: 'District 3', cityId: '1', isActive: true },
              { id: '4', name: 'District 4', cityId: '1', isActive: true },
              { id: '5', name: 'District 5', cityId: '1', isActive: true },
              { id: '6', name: 'District 6', cityId: '1', isActive: true },
              { id: '7', name: 'District 7', cityId: '1', isActive: true },
              { id: '8', name: 'District 8', cityId: '1', isActive: true },
              { id: '9', name: 'District 9', cityId: '1', isActive: true },
              { id: '10', name: 'District 10', cityId: '1', isActive: true },
              { id: '11', name: 'District 11', cityId: '1', isActive: true },
              { id: '12', name: 'District 12', cityId: '1', isActive: true },
              { id: '13', name: 'District 13', cityId: '1', isActive: true },
              { id: '14', name: 'District 14', cityId: '1', isActive: true },
              { id: '15', name: 'District 15', cityId: '1', isActive: true },
              { id: '16', name: 'District 16', cityId: '1', isActive: true },
              { id: '17', name: 'District 17', cityId: '1', isActive: true },
              { id: '18', name: 'District 18', cityId: '1', isActive: true },
              { id: '19', name: 'District 19', cityId: '1', isActive: true },
              { id: '20', name: 'District 20', cityId: '1', isActive: true },
              { id: '21', name: 'District 21', cityId: '1', isActive: true },
              { id: '22', name: 'District 22', cityId: '1', isActive: true }
            ]
          }
        ]
      },
      {
        id: '2',
        name: 'Herat',
        code: 'HRT',
        isActive: true,
        cities: [
          {
            id: '2',
            name: 'Herat',
            provinceId: '2',
            isActive: true,
            districts: [
              { id: '23', name: 'District 1', cityId: '2', isActive: true },
              { id: '24', name: 'District 2', cityId: '2', isActive: true },
              { id: '25', name: 'District 3', cityId: '2', isActive: true },
              { id: '26', name: 'District 4', cityId: '2', isActive: true },
              { id: '27', name: 'District 5', cityId: '2', isActive: true },
              { id: '28', name: 'District 6', cityId: '2', isActive: true },
              { id: '29', name: 'District 7', cityId: '2', isActive: true },
              { id: '30', name: 'District 8', cityId: '2', isActive: true },
              { id: '31', name: 'District 9', cityId: '2', isActive: true },
              { id: '32', name: 'District 10', cityId: '2', isActive: true },
              { id: '33', name: 'District 11', cityId: '2', isActive: true },
              { id: '34', name: 'District 12', cityId: '2', isActive: true },
              { id: '35', name: 'District 13', cityId: '2', isActive: true }
            ]
          }
        ]
      },
      {
        id: '3',
        name: 'Kandahar',
        code: 'KDH',
        isActive: true,
        cities: [
          {
            id: '3',
            name: 'Kandahar',
            provinceId: '3',
            isActive: true,
            districts: [
              { id: '36', name: 'District 1', cityId: '3', isActive: true },
              { id: '37', name: 'District 2', cityId: '3', isActive: true },
              { id: '38', name: 'District 3', cityId: '3', isActive: true },
              { id: '39', name: 'District 4', cityId: '3', isActive: true },
              { id: '40', name: 'District 5', cityId: '3', isActive: true },
              { id: '41', name: 'District 6', cityId: '3', isActive: true },
              { id: '42', name: 'District 7', cityId: '3', isActive: true },
              { id: '43', name: 'District 8', cityId: '3', isActive: true },
              { id: '44', name: 'District 9', cityId: '3', isActive: true }
            ]
          }
        ]
      },
      {
        id: '4',
        name: 'Balkh',
        code: 'BLK',
        isActive: true,
        cities: [
          {
            id: '4',
            name: 'Mazar-i-Sharif',
            provinceId: '4',
            isActive: true,
            districts: [
              { id: '45', name: 'District 1', cityId: '4', isActive: true },
              { id: '46', name: 'District 2', cityId: '4', isActive: true },
              { id: '47', name: 'District 3', cityId: '4', isActive: true },
              { id: '48', name: 'District 4', cityId: '4', isActive: true },
              { id: '49', name: 'District 5', cityId: '4', isActive: true },
              { id: '50', name: 'District 6', cityId: '4', isActive: true },
              { id: '51', name: 'District 7', cityId: '4', isActive: true },
              { id: '52', name: 'District 8', cityId: '4', isActive: true },
              { id: '53', name: 'District 9', cityId: '4', isActive: true },
              { id: '54', name: 'District 10', cityId: '4', isActive: true }
            ]
          }
        ]
      },
      {
        id: '5',
        name: 'Nangarhar',
        code: 'NGR',
        isActive: true,
        cities: [
          {
            id: '5',
            name: 'Jalalabad',
            provinceId: '5',
            isActive: true,
            districts: [
              { id: '55', name: 'District 1', cityId: '5', isActive: true },
              { id: '56', name: 'District 2', cityId: '5', isActive: true },
              { id: '57', name: 'District 3', cityId: '5', isActive: true },
              { id: '58', name: 'District 4', cityId: '5', isActive: true },
              { id: '59', name: 'District 5', cityId: '5', isActive: true },
              { id: '60', name: 'District 6', cityId: '5', isActive: true },
              { id: '61', name: 'District 7', cityId: '5', isActive: true },
              { id: '62', name: 'District 8', cityId: '5', isActive: true }
            ]
          }
        ]
      }
    ];

    res.json({
      success: true,
      data: provinces
    });
  } catch (error) {
    console.error('Error fetching provinces:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch provinces',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

// Property Features Configuration
router.get('/property-features', async (req: Request, res: Response) => {
  try {
    const propertyFeatures = [
      { id: '1', name: 'parking', label: 'Parking', category: 'feature', isActive: true },
      { id: '2', name: 'garden', label: 'Garden', category: 'feature', isActive: true },
      { id: '3', name: 'security', label: 'Security', category: 'feature', isActive: true },
      { id: '4', name: 'balcony', label: 'Balcony', category: 'feature', isActive: true },
      { id: '5', name: 'elevator', label: 'Elevator', category: 'feature', isActive: true },
      { id: '6', name: 'pool', label: 'Swimming Pool', category: 'amenity', isActive: true },
      { id: '7', name: 'gym', label: 'Gym', category: 'amenity', isActive: true },
      { id: '8', name: 'wifi', label: 'WiFi', category: 'amenity', isActive: true },
      { id: '9', name: 'heating', label: 'Heating', category: 'utility', isActive: true },
      { id: '10', name: 'ac', label: 'Air Conditioning', category: 'utility', isActive: true },
      { id: '11', name: 'generator', label: 'Generator', category: 'utility', isActive: true },
      { id: '12', name: 'furnished', label: 'Furnished', category: 'feature', isActive: true },
      { id: '13', name: 'kitchen', label: 'Modern Kitchen', category: 'feature', isActive: true },
      { id: '14', name: 'laundry', label: 'Laundry Room', category: 'feature', isActive: true },
      { id: '15', name: 'storage', label: 'Storage Room', category: 'feature', isActive: true },
      { id: '16', name: 'terrace', label: 'Terrace', category: 'feature', isActive: true },
      { id: '17', name: 'fireplace', label: 'Fireplace', category: 'feature', isActive: true }
    ];

    res.json({
      success: true,
      data: propertyFeatures
    });
  } catch (error) {
    console.error('Error fetching property features:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch property features',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

// Currencies Configuration
router.get('/currencies', async (req: Request, res: Response) => {
  try {
    const currencies = [
      { id: '1', code: 'AFN', name: 'Afghan Afghani', symbol: '؋', exchangeRate: 1, isDefault: true, isActive: true },
      { id: '2', code: 'USD', name: 'US Dollar', symbol: '$', exchangeRate: 0.014, isDefault: false, isActive: true },
      { id: '3', code: 'EUR', name: 'Euro', symbol: '€', exchangeRate: 0.012, isDefault: false, isActive: true }
    ];

    res.json({
      success: true,
      data: currencies
    });
  } catch (error) {
    console.error('Error fetching currencies:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch currencies',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

// System Statistics
router.get('/system-stats', async (req: Request, res: Response) => {
  try {
    // Get real statistics from database
    const [totalProperties, totalUsers] = await Promise.all([
      Property.countDocuments({ status: { $ne: 'DELETED' } }),
      User.countDocuments({ isActive: true })
    ]);

    // Calculate total cities (from provinces data)
    const totalCities = 5; // Based on our provinces configuration

    // Calculate years of experience (since platform launch)
    const launchDate = new Date('2023-01-01');
    const currentDate = new Date();
    const yearsExperience = Math.max(1, Math.floor((currentDate.getTime() - launchDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)));

    const systemStats = {
      totalProperties,
      totalUsers,
      totalCities,
      yearsExperience,
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: systemStats
    });
  } catch (error) {
    console.error('Error fetching system stats:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch system statistics',
        code: 'INTERNAL_ERROR'
      }
    });
  }
});

export default router;
