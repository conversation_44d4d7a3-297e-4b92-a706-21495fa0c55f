const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Additional test users
const additionalUsers = [
  {
    name: '<PERSON>',
    email: 'ali.re<PERSON><PERSON>@example.com',
    password: 'password123',
    phone: '+93701234572',
    role: 'USER',
    bio: 'Young professional looking for modern apartments.',
    location: 'Kabul, Afghanistan'
  },
  {
    name: '<PERSON><PERSON>',
    email: 'maryam.ho<PERSON><PERSON>@example.com',
    password: 'password123',
    phone: '+93701234573',
    role: 'AGENT',
    bio: 'Experienced agent specializing in residential properties.',
    location: 'Herat, Afghanistan'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    phone: '+93701234574',
    role: 'USER',
    bio: 'Family man searching for a safe neighborhood.',
    location: 'Kandahar, Afghanistan'
  }
];

// Additional properties
const additionalProperties = [
  {
    title: 'Cozy Studio Apartment',
    description: 'Perfect for students or young professionals. Fully furnished with modern amenities. Close to university and shopping centers.',
    price: 25000,
    currency: 'USD',
    category: 'SALE',
    type: 'APARTMENT',
    province: 'Kabul',
    city: 'Kabul',
    district: 'District 4',
    address: 'Student Complex, Building 2, District 4, Kabul',
    bedrooms: 1,
    bathrooms: 1,
    area: 45,
    yearBuilt: 2019,
    furnished: true,
    parking: false,
    garden: false,
    balcony: true,
    features: ['BALCONY', 'FURNISHED']
  },
  {
    title: 'Traditional Afghan House',
    description: 'Beautiful traditional house with courtyard. Authentic architecture with modern conveniences. Great for large families.',
    price: 65000,
    currency: 'USD',
    category: 'SALE',
    type: 'HOUSE',
    province: 'Herat',
    city: 'Herat',
    district: 'District 2',
    address: 'Old City, Traditional Quarter, Herat',
    bedrooms: 6,
    bathrooms: 3,
    area: 300,
    yearBuilt: 2010,
    furnished: false,
    parking: true,
    garden: true,
    balcony: false,
    features: ['PARKING', 'GARDEN', 'COURTYARD']
  },
  {
    title: 'Modern Office Building',
    description: 'Brand new office building with elevator and parking. Multiple floors available for rent. Prime business location.',
    price: 2000,
    currency: 'USD',
    category: 'RENT',
    type: 'COMMERCIAL',
    province: 'Kabul',
    city: 'Kabul',
    district: 'District 1',
    address: 'Business District, Main Street, Kabul',
    bedrooms: 0,
    bathrooms: 4,
    area: 200,
    yearBuilt: 2021,
    furnished: false,
    parking: true,
    garden: false,
    balcony: false,
    features: ['PARKING', 'ELEVATOR', 'SECURITY', 'AC']
  }
];

async function createUser(userData) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/register`, userData);
    console.log(`✅ Created user: ${userData.name}`);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log(`ℹ️  User ${userData.name} already exists`);
      return { data: { user: userData } };
    }
    console.error(`❌ Failed to create user ${userData.name}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function loginUser(email, password) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, { email, password });
    return response.data.data.accessToken;
  } catch (error) {
    console.error(`❌ Failed to login user ${email}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function createProperty(propertyData, token) {
  try {
    const response = await axios.post(`${BASE_URL}/properties`, propertyData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ Created property: ${propertyData.title}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to create property ${propertyData.title}:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function getProperties() {
  try {
    const response = await axios.get(`${BASE_URL}/properties`);
    return response.data.data || [];
  } catch (error) {
    console.error('❌ Failed to get properties:', error.response?.data?.message || error.message);
    return [];
  }
}

async function addToFavorites(propertyId, token) {
  try {
    const response = await axios.post(`${BASE_URL}/users/favorites`, { propertyId }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ Added property to favorites`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to add to favorites:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function sendMessage(messageData, token) {
  try {
    const response = await axios.post(`${BASE_URL}/messages`, messageData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ Sent message about property`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to send message:`, error.response?.data?.message || error.message);
    return null;
  }
}

async function addMoreData() {
  console.log('🌱 Adding more test data to database...\n');

  try {
    // Create additional users
    console.log('👥 Creating additional users...');
    const newUsers = [];
    for (const userData of additionalUsers) {
      const user = await createUser(userData);
      if (user) {
        newUsers.push({ ...userData, ...user });
      }
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Create additional properties
    console.log('\n🏠 Creating additional properties...');
    let propertyCount = 0;
    const createdProperties = [];
    
    for (let i = 0; i < additionalProperties.length; i++) {
      const propertyData = additionalProperties[i];
      
      // Use existing users (try to login with known users)
      const userEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        ...newUsers.map(u => u.email)
      ];
      
      const userEmail = userEmails[i % userEmails.length];
      const token = await loginUser(userEmail, 'password123');
      
      if (token) {
        const property = await createProperty(propertyData, token);
        if (property) {
          propertyCount++;
          createdProperties.push(property);
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Get all properties for testing favorites and messages
    console.log('\n📋 Getting all properties...');
    const allProperties = await getProperties();
    console.log(`Found ${allProperties.length} total properties in database`);

    // Add some favorites
    console.log('\n❤️  Adding some favorites...');
    if (allProperties.length > 0) {
      const userToken = await loginUser('<EMAIL>', 'password123');
      if (userToken && allProperties.length > 0) {
        await addToFavorites(allProperties[0].id, userToken);
        if (allProperties.length > 1) {
          await addToFavorites(allProperties[1].id, userToken);
        }
      }
    }

    // Send some test messages
    console.log('\n💬 Creating some test messages...');
    if (allProperties.length > 0) {
      const senderToken = await loginUser('<EMAIL>', 'password123');
      if (senderToken) {
        const messageData = {
          receiverId: allProperties[0].owner?.id || 'unknown',
          propertyId: allProperties[0].id,
          subject: 'Inquiry about your property',
          content: 'Hi, I am interested in your property. Could you please provide more details about the neighborhood and availability for viewing? Thank you.'
        };
        await sendMessage(messageData, senderToken);
      }
    }

    console.log('\n🎉 Additional data added successfully!');
    console.log('\n📊 Final Summary:');
    console.log(`   Total Properties: ${allProperties.length}`);
    console.log(`   New Properties Added: ${propertyCount}`);
    console.log(`   New Users Added: ${newUsers.length}`);
    
    console.log('\n🔗 Test Accounts:');
    console.log('   Agent: <EMAIL> / password123');
    console.log('   Agent: <EMAIL> / password123');
    console.log('   Agent: <EMAIL> / password123');
    console.log('   User: <EMAIL> / password123');
    console.log('   User: <EMAIL> / password123');
    console.log('   User: <EMAIL> / password123');
    console.log('   User: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Failed to add more data:', error.message);
  }
}

// Run the script
addMoreData();
